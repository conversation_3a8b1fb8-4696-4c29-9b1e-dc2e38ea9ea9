# InspirFlow API服务

这是InspirFlow项目的安全LLM代理API服务，运行在5002端口。该服务提供统一的API接口来访问多个大语言模型平台。

## 核心功能

- **安全认证**：基于API密钥的应用认证机制
- **模型授权**：每个应用只能访问其被授权的模型
- **多平台支持**：支持OpenAI、Anthropic、DeepSeek、阿里云等多个平台
- **流式响应**：完整支持流式和非流式响应
- **统一接口**：提供标准的OpenAI兼容API接口

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 初始化数据库

```bash
python manage_api_db.py init
```

### 3. 创建应用和API密钥

```bash
# 创建新应用
python manage_api_db.py create-app "我的应用" --description "测试应用"

# 查看所有应用
python manage_api_db.py list-apps
```

### 4. 为应用添加模型授权

```bash
# 为应用ID为1的应用添加模型ID为1的授权，并设为默认模型
python manage_api_db.py add-model 1 1 --default

# 查看应用的授权模型
python manage_api_db.py show-models 1
```

### 5. 启动API服务

```bash
# 开发模式
python start_api_service.py --mode dev

# 生产模式
python start_api_service.py --mode prod --workers 4
```

## API端点

### 健康检查

```bash
curl http://localhost:5002/api/v1/health
```

### 获取授权模型列表

```bash
curl -X GET "http://localhost:5002/api/v1/models" \
     -H "Authorization: Bearer YOUR_API_KEY"
```

### 聊天完成 (非流式)

```bash
curl -X POST "http://localhost:5002/api/v1/chat/completions" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_API_KEY" \
     -d '{
       "model": "gpt-4-turbo",
       "messages": [
         {"role": "user", "content": "Hello!"}
       ]
     }'
```

### 聊天完成 (流式)

```bash
curl -X POST "http://localhost:5002/api/v1/chat/completions" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_API_KEY" \
     -d '{
       "messages": [
         {"role": "user", "content": "Tell me a joke."}
       ],
       "stream": true
     }' --no-buffer
```

## 测试

### 运行测试套件

```bash
# 运行所有测试
python test_api_service.py --api-key YOUR_API_KEY

# 测试特定功能
python test_api_service.py --test health
python test_api_service.py --test models --api-key YOUR_API_KEY
python test_api_service.py --test chat --api-key YOUR_API_KEY --model "gpt-4-turbo"
```

### 验证API密钥

```bash
python manage_api_db.py test-key YOUR_API_KEY
```

## Docker部署

### 使用Docker Compose

```bash
# 构建并启动服务
docker-compose -f docker-compose-api.yml up --build

# 后台运行
docker-compose -f docker-compose-api.yml up -d --build
```

这将同时启动：
- Web应用 (端口8050)
- API服务 (端口5002)

### 单独运行API服务

```bash
# 构建镜像
docker build -t inspirflow-api .

# 运行API服务
docker run -p 5002:5002 \
  -e MARIADB_HOST=your_db_host \
  -e MARIADB_USER=your_db_user \
  -e MARIADB_PASSWORD=your_db_password \
  inspirflow-api \
  gunicorn --bind 0.0.0.0:5002 --workers 4 api_wsgi:application
```

## 管理命令

### 数据库管理

```bash
# 初始化数据库
python manage_api_db.py init

# 创建应用
python manage_api_db.py create-app "应用名称" --description "描述"

# 列出所有应用
python manage_api_db.py list-apps

# 为应用添加模型授权
python manage_api_db.py add-model APP_ID MODEL_ID --default

# 查看应用的模型
python manage_api_db.py show-models APP_ID

# 测试API密钥
python manage_api_db.py test-key API_KEY
```

## 支持的平台

当前支持以下LLM平台：

- **OpenAI** - 标准OpenAI API
- **Anthropic** - Claude系列模型
- **DeepSeek** - DeepSeek模型
- **DashScope** - 阿里云通义千问
- **OpenRouter** - OpenRouter平台

## 配置

### 环境变量

- `MARIADB_HOST` - 数据库主机地址
- `MARIADB_PORT` - 数据库端口 (默认3306)
- `MARIADB_USER` - 数据库用户名
- `MARIADB_PASSWORD` - 数据库密码
- `MARIADB_MODEL_DB` - 模型数据库名 (默认model_registry)
- `MARIADB_CHAT_DB` - 聊天数据库名 (默认chat_system)

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置和网络连接
   - 确认数据库服务正在运行

2. **API密钥验证失败**
   - 确认API密钥格式正确
   - 检查应用是否存在且激活

3. **模型访问被拒绝**
   - 确认应用有该模型的授权
   - 检查模型名称是否正确

4. **代理请求失败**
   - 检查目标平台的API密钥和URL配置
   - 确认网络连接正常

### 日志查看

```bash
# 查看服务日志
tail -f /var/log/inspirflow-api.log

# Docker环境查看日志
docker-compose -f docker-compose-api.yml logs -f api-service
```

## 开发

### 添加新平台支持

1. 在 `platform_adapters.py` 中创建新的适配器类
2. 继承 `PlatformAdapter` 基类
3. 实现必要的方法：`adapt_request`, `get_endpoint_url`, `get_headers`
4. 在 `PlatformAdapterFactory` 中注册新适配器

### 扩展API功能

API服务基于Flask构建，可以轻松添加新的端点和功能。主要文件：

- `api_service.py` - 主要的Flask应用
- `api_db_operations.py` - 数据库操作
- `platform_adapters.py` - 平台适配器
- `api_wsgi.py` - WSGI入口点
