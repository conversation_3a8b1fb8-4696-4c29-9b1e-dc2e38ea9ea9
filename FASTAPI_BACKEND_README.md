# InspirFlow FastAPI 后端 - 前后端分离版本

这是InspirFlow项目的前后端分离版本，使用FastAPI作为后端，提供RESTful API接口，前端使用纯HTML/CSS/JavaScript实现。

## 项目架构

### 原架构 vs 新架构

**原架构（Dash + Flask）:**
- 前端：Dash框架（基于React）+ Bootstrap组件
- 后端：Flask + 两个MariaDB数据库
- 部署：单体应用，前后端耦合

**新架构（前后端分离）:**
- 前端：纯HTML/CSS/JavaScript + Bootstrap
- 后端：FastAPI + 两个MariaDB数据库
- 部署：前后端独立部署，通过API通信

### 目录结构

```
├── fastapi_backend/           # FastAPI后端
│   ├── main.py               # 主应用文件
│   ├── dependencies.py       # 依赖注入
│   ├── models/
│   │   └── schemas.py        # Pydantic模型
│   └── routes/               # API路由
│       ├── auth.py           # 认证API
│       ├── users.py          # 用户管理API
│       ├── conversations.py  # 对话管理API
│       ├── messages.py       # 消息管理API
│       ├── models.py         # 模型管理API
│       ├── admin.py          # 管理员API
│       └── chat.py           # 聊天API（兼容OpenAI）
├── frontend/                 # 前端文件
│   ├── index.html            # 主页面
│   └── static/
│       ├── css/
│       │   └── style.css     # 样式文件
│       └── js/
│           └── app.js        # 主应用脚本
├── start_fastapi_backend.py  # 启动脚本
└── requirements.txt          # 依赖文件（已更新）
```

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 启动FastAPI后端

```bash
# 开发模式（推荐）
python start_fastapi_backend.py --mode dev

# 生产模式
python start_fastapi_backend.py --mode prod --workers 4

# 自定义端口
python start_fastapi_backend.py --port 8080
```

### 3. 访问应用

- 前端界面: http://localhost:8000
- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

## API接口文档

### 认证接口

- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/verify` - 验证API密钥

### 用户管理接口

- `GET /api/v1/users/me` - 获取当前用户信息
- `GET /api/v1/users/me/stats` - 获取用户统计信息
- `PUT /api/v1/users/me/settings` - 更新用户设置

### 对话管理接口

- `GET /api/v1/conversations/` - 获取对话列表
- `POST /api/v1/conversations/` - 创建新对话
- `GET /api/v1/conversations/{id}` - 获取指定对话
- `DELETE /api/v1/conversations/{id}` - 删除对话

### 消息管理接口

- `GET /api/v1/messages/conversation/{id}` - 获取对话消息
- `POST /api/v1/messages/send` - 发送消息
- `PUT /api/v1/messages/{id}` - 更新消息
- `DELETE /api/v1/messages/{id}` - 删除消息

### 模型管理接口

- `GET /api/v1/models/` - 获取模型列表
- `GET /api/v1/models/user/current` - 获取用户当前模型设置
- `PUT /api/v1/models/user/current` - 更新用户模型设置

### 管理员接口

- `GET /api/v1/admin/users` - 获取所有用户（管理员）
- `POST /api/v1/admin/users` - 创建用户（管理员）
- `POST /api/v1/admin/users/balance` - 更新用户余额（管理员）
- `POST /api/v1/admin/users/toggle-status` - 切换用户状态（管理员）

### 聊天接口（兼容OpenAI API）

- `POST /api/v1/chat/completions` - 聊天完成（支持流式）
- `GET /api/v1/chat/models` - 获取模型列表

## 功能特性

### 前端功能

1. **用户认证**
   - API密钥登录
   - JWT令牌管理
   - 自动登录状态保持

2. **对话管理**
   - 创建新对话
   - 对话列表显示
   - 对话选择和切换
   - 对话删除

3. **消息功能**
   - 发送文本消息
   - 图片上传支持
   - 消息历史显示
   - 实时消息渲染

4. **模型设置**
   - 模型选择
   - 温度调节
   - 实时设置保存

5. **用户设置**
   - MathJax开关
   - 用户统计查看
   - 设置持久化

6. **管理员功能**
   - 用户管理
   - 用户创建
   - 余额管理
   - 状态切换

### 后端功能

1. **RESTful API**
   - 标准HTTP方法
   - JSON数据格式
   - 统一错误处理

2. **认证授权**
   - JWT令牌认证
   - 权限级别控制
   - API密钥验证

3. **数据库操作**
   - 复用现有数据库模型
   - 事务管理
   - 连接池优化

4. **OpenAI兼容**
   - 兼容OpenAI API格式
   - 支持流式响应
   - 多模型支持

## 部署说明

### 开发环境

```bash
# 启动开发服务器
python start_fastapi_backend.py --mode dev
```

### 生产环境

```bash
# 使用Uvicorn启动
python start_fastapi_backend.py --mode prod --workers 4

# 或者直接使用Uvicorn
uvicorn fastapi_backend.main:app --host 0.0.0.0 --port 8000 --workers 4
```

### Docker部署

可以基于现有的Dockerfile进行修改，添加FastAPI后端的启动命令。

### Nginx配置

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # API接口
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 迁移指南

### 从Dash版本迁移

1. **数据库兼容性**
   - 新版本完全兼容现有数据库结构
   - 无需数据迁移
   - 用户数据和对话历史保持不变

2. **功能对比**
   - ✅ 用户认证和管理
   - ✅ 对话创建和管理
   - ✅ 消息发送和显示
   - ✅ 模型选择和设置
   - ✅ 管理员功能
   - ✅ 图片上传支持
   - ✅ MathJax数学公式渲染

3. **API兼容性**
   - 保持与原有API服务的兼容性
   - 可以同时运行两个版本

### 切换步骤

1. 停止原Dash应用
2. 安装新依赖：`pip install fastapi uvicorn`
3. 启动FastAPI后端：`python start_fastapi_backend.py`
4. 访问新界面：http://localhost:8000

## 故障排除

### 常见问题

1. **端口冲突**
   - 默认端口8000，可通过`--port`参数修改
   - 确保端口未被其他服务占用

2. **数据库连接失败**
   - 检查数据库配置和网络连接
   - 确认数据库服务正在运行

3. **静态文件404**
   - 确保frontend目录结构正确
   - 检查文件路径和权限

4. **API认证失败**
   - 确认API密钥格式正确
   - 检查JWT令牌是否过期

### 日志查看

```bash
# 查看应用日志
python start_fastapi_backend.py --mode dev

# 生产环境日志
uvicorn fastapi_backend.main:app --log-level info
```

## 开发指南

### 添加新API端点

1. 在`fastapi_backend/routes/`目录下创建新的路由文件
2. 定义Pydantic模型在`fastapi_backend/models/schemas.py`
3. 在`fastapi_backend/main.py`中注册路由

### 前端开发

1. 修改`frontend/static/js/app.js`添加新功能
2. 更新`frontend/static/css/style.css`调整样式
3. 在`frontend/index.html`中添加新的UI组件

### 测试

- API测试：访问 http://localhost:8000/docs
- 前端测试：访问 http://localhost:8000
- 健康检查：访问 http://localhost:8000/health

## 性能优化

1. **后端优化**
   - 使用连接池管理数据库连接
   - 启用Gzip压缩
   - 配置适当的worker数量

2. **前端优化**
   - 静态资源缓存
   - 图片压缩和懒加载
   - API请求防抖

3. **部署优化**
   - 使用CDN加速静态资源
   - 配置负载均衡
   - 启用HTTP/2

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

与原项目保持一致的许可证。
