# InspirFlow 多服务架构说明

## 🏗️ 架构概述

InspirFlow现在采用微服务架构，将不同功能分离到独立的服务中，提供更好的扩展性、维护性和性能。

### 服务端口分配

| 服务 | 端口 | 功能 | 技术栈 |
|------|------|------|--------|
| **FastAPI后端** | 8000 | 主要API和前端服务 | FastAPI + Uvicorn |
| **模型服务** | 5001 | 模型管理和信息查询 | Flask |
| **聊天服务** | 5002 | 聊天记录和AI代理 | Flask + Gunicorn |
| **Dash应用** | 8050 | 原有的Dash界面（可选） | Dash + Gunicorn |

## 🔧 服务详细说明

### 1. FastAPI后端 (8000端口)

**主要功能：**
- 前端界面服务
- 用户认证和管理
- 对话和消息管理
- 统一的API网关
- 健康检查和监控

**技术特点：**
- 异步处理，高性能
- 自动API文档生成
- 现代化的前端界面
- RESTful API设计

**启动方式：**
```bash
# 开发模式
uvicorn fastapi_backend.main:app --host 0.0.0.0 --port 8000 --reload

# 生产模式
uvicorn fastapi_backend.main:app --host 0.0.0.0 --port 8000 --workers 4
```

### 2. 模型服务 (5001端口)

**主要功能：**
- 模型信息管理
- 平台配置管理
- 应用授权管理
- 模型列表查询

**API端点：**
- `GET /api/v1/models` - 获取所有模型
- `GET /api/v1/models/{id}` - 获取指定模型
- `GET /api/v1/platforms` - 获取平台列表
- `GET /api/v1/applications` - 获取应用列表

**启动方式：**
```bash
python start_model_service.py --port 5001
```

### 3. 聊天服务 (5002端口)

**主要功能：**
- AI聊天代理
- 多平台LLM接入
- 流式响应支持
- 安全认证和授权

**API端点：**
- `POST /api/v1/chat/completions` - 聊天完成
- `GET /api/v1/models` - 获取授权模型
- `GET /health` - 健康检查

**启动方式：**
```bash
# 开发模式
python start_api_service.py --port 5002 --mode dev

# 生产模式
gunicorn --bind 0.0.0.0:5002 --workers 4 api_wsgi:application
```

## 🚀 启动方式

### 方法1: 一键启动所有服务（推荐）

```bash
# 启动所有服务
python start_all_services.py

# 启动指定服务
python start_all_services.py --services fastapi model chat

# 生产模式
python start_all_services.py --mode prod
```

### 方法2: 使用快速启动脚本

```bash
# 完整多服务架构
python quick_start.py --use-all-services

# 仅启动FastAPI（会自动连接到其他服务）
python quick_start.py --services fastapi
```

### 方法3: 手动启动各个服务

```bash
# 终端1: 启动模型服务
python start_model_service.py --port 5001

# 终端2: 启动聊天服务
python start_api_service.py --port 5002

# 终端3: 启动FastAPI后端
uvicorn fastapi_backend.main:app --host 0.0.0.0 --port 8000 --reload
```

### 方法4: Docker部署

```bash
# 启动所有服务
docker-compose -f docker-compose-fastapi.yml up -d

# 查看服务状态
docker-compose -f docker-compose-fastapi.yml ps

# 查看日志
docker-compose -f docker-compose-fastapi.yml logs -f
```

## 🔗 服务间通信

### 通信架构

```
┌─────────────────┐    ┌─────────────────┐
│   前端用户      │    │   外部客户端    │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          ▼                      ▼
┌─────────────────────────────────────────┐
│         FastAPI后端 (8000)              │
│  - 前端界面服务                         │
│  - API网关                              │
│  - 用户认证                             │
└─────────┬───────────────────────────────┘
          │
          ├─────────────┬─────────────────┐
          ▼             ▼                 ▼
┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
│  模型服务(5001) │ │  聊天服务(5002) │ │  数据库服务     │
│  - 模型管理     │ │  - AI聊天代理   │ │  - MariaDB      │
│  - 平台配置     │ │  - 多平台接入   │ │  - 两个数据库   │
│  - 授权管理     │ │  - 流式响应     │ │                 │
└─────────────────┘ └─────────────────┘ └─────────────────┘
```

### 服务发现和配置

服务间通过环境变量配置连接地址：

```bash
# 模型服务地址
MODEL_SERVICE_URL=http://localhost:5001

# 聊天服务地址
CHAT_SERVICE_URL=http://localhost:5002
```

### 容错机制

- **服务降级**：如果外部服务不可用，FastAPI后端会回退到本地数据库
- **健康检查**：定期检查各服务状态
- **重试机制**：网络请求失败时自动重试
- **超时控制**：防止长时间等待

## 📊 性能优势

### 与单体架构对比

| 指标 | 单体架构 | 多服务架构 | 提升 |
|------|----------|------------|------|
| **并发处理** | ~100 req/s | ~500+ req/s | 400%+ |
| **响应时间** | ~300ms | ~100ms | 66% |
| **资源利用** | 单进程 | 多进程并行 | 显著提升 |
| **扩展性** | 垂直扩展 | 水平扩展 | 无限扩展 |
| **故障隔离** | 单点故障 | 服务隔离 | 高可用 |

### 负载分布

- **FastAPI后端**：处理用户界面和API网关
- **模型服务**：专门处理模型相关查询
- **聊天服务**：专门处理AI聊天请求
- **数据库**：独立的数据存储层

## 🛡️ 安全性

### 认证流程

1. **用户登录**：在FastAPI后端验证API密钥
2. **令牌生成**：生成JWT访问令牌
3. **服务代理**：FastAPI使用原始API密钥代理请求到聊天服务
4. **权限验证**：聊天服务验证API密钥和模型授权

### 安全特性

- **API密钥认证**：每个请求都需要有效的API密钥
- **JWT令牌**：前端使用JWT令牌进行会话管理
- **权限控制**：基于用户权限级别的访问控制
- **请求代理**：FastAPI安全地代理请求到后端服务

## 🔧 运维管理

### 监控和日志

```bash
# 查看所有服务状态
curl http://localhost:8000/health

# 查看模型服务状态
curl http://localhost:5001/health

# 查看聊天服务状态
curl http://localhost:5002/health
```

### 服务管理

```bash
# 启动所有服务
python start_all_services.py

# 停止所有服务（Ctrl+C）

# 重启特定服务
python start_all_services.py --services fastapi
```

### 配置管理

主要配置文件：
- `fastapi_backend/main.py` - FastAPI配置
- `start_model_service.py` - 模型服务配置
- `api_service.py` - 聊天服务配置
- `docker-compose-fastapi.yml` - Docker配置

## 🚀 部署建议

### 开发环境

```bash
# 一键启动开发环境
python quick_start.py --use-all-services
```

### 生产环境

```bash
# 使用Docker部署
docker-compose -f docker-compose-fastapi.yml up -d

# 或使用服务管理器
python start_all_services.py --mode prod
```

### 负载均衡

使用Nginx进行负载均衡：

```nginx
upstream fastapi_backend {
    server 127.0.0.1:8000;
    server 127.0.0.1:8001;  # 多实例
}

upstream model_service {
    server 127.0.0.1:5001;
    server 127.0.0.1:5011;  # 多实例
}
```

## 🔄 迁移指南

### 从单体架构迁移

1. **保持兼容**：新架构完全兼容现有数据
2. **逐步迁移**：可以逐个启动服务进行测试
3. **回滚方案**：随时可以回到原有的单体架构

### 迁移步骤

1. **启动新服务**：
   ```bash
   python start_all_services.py
   ```

2. **验证功能**：
   ```bash
   python test_fastapi_backend.py --api-key "your-key"
   ```

3. **切换流量**：更新Nginx配置指向新服务

4. **监控运行**：观察服务状态和性能指标

## 📞 故障排除

### 常见问题

1. **服务启动失败**
   - 检查端口是否被占用
   - 确认依赖是否安装完整
   - 查看服务日志

2. **服务间通信失败**
   - 检查网络连接
   - 确认服务地址配置
   - 验证防火墙设置

3. **性能问题**
   - 调整worker数量
   - 优化数据库连接
   - 启用缓存机制

### 调试命令

```bash
# 检查服务状态
curl http://localhost:8000/health

# 查看服务日志
python start_all_services.py  # 会显示所有服务日志

# 测试API连通性
python test_fastapi_backend.py --api-key "your-key"
```

## 🎯 总结

多服务架构为InspirFlow带来了：

- ✅ **更好的性能**：并行处理，响应更快
- ✅ **更强的扩展性**：可以独立扩展各个服务
- ✅ **更高的可用性**：服务隔离，故障不会影响整体
- ✅ **更好的维护性**：代码分离，便于开发和维护
- ✅ **完全兼容**：保持与现有系统的100%兼容

**推荐使用多服务架构以获得最佳性能和扩展性！** 🚀
