# InspirFlow 故障排除指南

## 🚨 常见问题及解决方案

### 1. 缺少gunicorn依赖

**错误信息：**
```
缺少必要的命令: gunicorn
```

**解决方案：**
```bash
# 安装gunicorn
pip install gunicorn

# 或安装所有依赖
pip install -r requirements.txt
```

### 2. 数据库连接失败

**错误信息：**
```
Not an executable object: 'SELECT 1'
```

**解决方案：**
这是SQLAlchemy版本兼容性问题，已在代码中修复。如果仍有问题：

```bash
# 跳过数据库检查启动
python quick_start.py --skip-db-check

# 或使用简化启动
python start_simple.py
```

### 3. 端口被占用

**错误信息：**
```
[Errno 10048] Only one usage of each socket address is normally permitted
```

**解决方案：**
```bash
# 使用不同端口
python start_simple.py --port 8001

# 或查找并终止占用端口的进程
netstat -ano | findstr :8000
taskkill /PID <进程ID> /F
```

### 4. 模块导入失败

**错误信息：**
```
ModuleNotFoundError: No module named 'fastapi'
```

**解决方案：**
```bash
# 确保在正确的虚拟环境中
# 安装所有依赖
pip install fastapi uvicorn gunicorn httpx flask flask-cors pydantic python-multipart

# 或使用requirements.txt
pip install -r requirements.txt
```

### 5. 权限错误（Windows）

**错误信息：**
```
PermissionError: [WinError 5] Access is denied
```

**解决方案：**
- 以管理员身份运行命令提示符
- 或使用用户级别的Python安装

## 🛠️ 推荐的启动方式

### 方式1: 最简单启动（推荐新手）

```bash
# Windows
start.bat

# Linux/Mac
python start_simple.py
```

### 方式2: 快速启动脚本

```bash
# 基础启动
python quick_start.py

# 跳过检查
python quick_start.py --skip-deps --skip-db-check
```

### 方式3: 直接启动FastAPI

```bash
# 开发模式
uvicorn fastapi_backend.main:app --host 0.0.0.0 --port 8000 --reload

# 如果uvicorn不可用
python -m uvicorn fastapi_backend.main:app --host 0.0.0.0 --port 8000 --reload
```

## 🔍 调试步骤

### 1. 检查Python环境

```bash
python --version
pip --version
```

### 2. 检查依赖安装

```bash
pip list | grep fastapi
pip list | grep uvicorn
pip list | grep gunicorn
```

### 3. 检查端口占用

```bash
# Windows
netstat -ano | findstr :8000

# Linux/Mac
lsof -i :8000
```

### 4. 检查文件结构

确保以下文件存在：
- `fastapi_backend/main.py`
- `fastapi_backend/routes/`
- `frontend/index.html`
- `requirements.txt`

## 🚀 快速修复命令

```bash
# 一键修复依赖
pip install fastapi uvicorn gunicorn httpx flask flask-cors pydantic python-multipart sqlalchemy pymysql

# 创建必要目录
mkdir -p fastapi_backend/models fastapi_backend/routes fastapi_backend/services
mkdir -p frontend/static/css frontend/static/js

# 启动服务（最简单方式）
python start_simple.py --skip-deps
```

## 📞 获取帮助

如果以上方法都无法解决问题：

1. **查看详细日志**：
   ```bash
   python start_simple.py --mode simple
   ```

2. **检查系统环境**：
   - Python版本 >= 3.8
   - pip版本最新
   - 网络连接正常

3. **使用最小化配置**：
   ```bash
   # 仅启动FastAPI，不连接外部服务
   python start_simple.py --mode simple --skip-deps
   ```

4. **查看错误详情**：
   启动时会显示详细的错误信息，请根据具体错误信息进行排查。

## 🎯 成功启动的标志

当看到以下信息时，说明启动成功：

```
🎉 服务启动中...
========================================
📱 前端界面: http://0.0.0.0:8000
📚 API文档: http://0.0.0.0:8000/docs
========================================
按 Ctrl+C 停止服务
========================================
INFO:     Started server process [xxxxx]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
```

然后可以在浏览器中访问 http://localhost:8000 查看应用。
