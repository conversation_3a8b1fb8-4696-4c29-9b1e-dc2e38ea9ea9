# api_db_operations.py - API服务专用数据库操作
import logging
from typing import Optional, Dict, Any, List
from sqlalchemy.exc import SQLAlchemyError

from db_models import (
    Application, AppModel, AIModel, Platform,
    model_db_session, ModelDBSession
)

logger = logging.getLogger(__name__)


class APIDBOperations:
    """API服务数据库操作类"""
    
    @staticmethod
    def verify_api_key(api_key: str) -> Optional[Application]:
        """验证API密钥并返回应用信息"""
        session = ModelDBSession()
        try:
            application = session.query(Application).filter_by(
                api_key=api_key
            ).first()
            return application
        except SQLAlchemyError as e:
            logger.error(f"验证API密钥失败: {e}")
            return None
        finally:
            session.close()
    
    @staticmethod
    def get_application_models(application_id: int) -> List[Dict[str, Any]]:
        """获取应用授权的模型列表"""
        session = ModelDBSession()
        try:
            app_models = session.query(AppModel).filter_by(
                application_id=application_id
            ).join(AIModel).join(Platform).all()
            
            models = []
            for app_model in app_models:
                model_info = {
                    'id': app_model.model.id,
                    'name': app_model.model.display_name,
                    'internal_name': app_model.model.internal_name,
                    'is_default': app_model.is_default,
                    'platform': app_model.model.platform.name,
                    'supports_vision': app_model.model.is_visible_model,
                    'input_token_price': float(app_model.model.input_token_price),
                    'output_token_price': float(app_model.model.output_token_price),
                    'free': app_model.model.free
                }
                models.append(model_info)
            
            return models
            
        except SQLAlchemyError as e:
            logger.error(f"获取应用模型失败: {e}")
            return []
        finally:
            session.close()
    
    @staticmethod
    def get_model_by_name(application_id: int, model_name: str) -> Optional[Dict[str, Any]]:
        """根据模型名称获取模型信息"""
        session = ModelDBSession()
        try:
            app_model = session.query(AppModel).filter_by(
                application_id=application_id
            ).join(AIModel).filter(
                AIModel.display_name == model_name
            ).first()
            
            if not app_model:
                return None
            
            return {
                'id': app_model.model.id,
                'name': app_model.model.display_name,
                'internal_name': app_model.model.internal_name,
                'platform': app_model.model.platform.name,
                'platform_api_key': app_model.model.platform.api_key,
                'platform_base_url': app_model.model.platform.base_url,
                'supports_vision': app_model.model.is_visible_model,
                'input_token_price': float(app_model.model.input_token_price),
                'output_token_price': float(app_model.model.output_token_price),
                'free': app_model.model.free
            }
            
        except SQLAlchemyError as e:
            logger.error(f"获取模型信息失败: {e}")
            return None
        finally:
            session.close()
    
    @staticmethod
    def get_default_model(application_id: int) -> Optional[Dict[str, Any]]:
        """获取应用的默认模型"""
        session = ModelDBSession()
        try:
            app_model = session.query(AppModel).filter_by(
                application_id=application_id,
                is_default=True
            ).first()
            
            if not app_model:
                return None
            
            return {
                'id': app_model.model.id,
                'name': app_model.model.display_name,
                'internal_name': app_model.model.internal_name,
                'platform': app_model.model.platform.name,
                'platform_api_key': app_model.model.platform.api_key,
                'platform_base_url': app_model.model.platform.base_url,
                'supports_vision': app_model.model.is_visible_model,
                'input_token_price': float(app_model.model.input_token_price),
                'output_token_price': float(app_model.model.output_token_price),
                'free': app_model.model.free
            }
            
        except SQLAlchemyError as e:
            logger.error(f"获取默认模型失败: {e}")
            return None
        finally:
            session.close()
    
    @staticmethod
    def check_model_authorization(application_id: int, model_name: str) -> bool:
        """检查应用是否有权限访问指定模型"""
        session = ModelDBSession()
        try:
            app_model = session.query(AppModel).filter_by(
                application_id=application_id
            ).join(AIModel).filter(
                AIModel.display_name == model_name
            ).first()
            
            return app_model is not None
            
        except SQLAlchemyError as e:
            logger.error(f"检查模型授权失败: {e}")
            return False
        finally:
            session.close()
    
    @staticmethod
    def get_platform_info(platform_id: int) -> Optional[Dict[str, Any]]:
        """获取平台信息"""
        session = ModelDBSession()
        try:
            platform = session.query(Platform).filter_by(id=platform_id).first()
            
            if not platform:
                return None
            
            return {
                'id': platform.id,
                'name': platform.name,
                'base_url': platform.base_url,
                'api_key': platform.api_key
            }
            
        except SQLAlchemyError as e:
            logger.error(f"获取平台信息失败: {e}")
            return None
        finally:
            session.close()
    
    @staticmethod
    def list_all_applications() -> List[Dict[str, Any]]:
        """列出所有应用（用于管理）"""
        session = ModelDBSession()
        try:
            applications = session.query(Application).all()
            
            result = []
            for app in applications:
                app_info = {
                    'id': app.id,
                    'name': app.name,
                    'description': app.description,
                    'api_key': app.api_key,
                    'created_at': app.created_at.isoformat() if app.created_at else None,
                    'model_count': len(app.app_models)
                }
                result.append(app_info)
            
            return result
            
        except SQLAlchemyError as e:
            logger.error(f"列出应用失败: {e}")
            return []
        finally:
            session.close()
    
    @staticmethod
    def create_application(name: str, description: str = None, api_key: str = None) -> Optional[Dict[str, Any]]:
        """创建新应用"""
        session = ModelDBSession()
        try:
            # 如果没有提供API密钥，生成一个
            if not api_key:
                import secrets
                import string
                alphabet = string.ascii_letters + string.digits
                api_key = 'sk-' + ''.join(secrets.choice(alphabet) for _ in range(48))
            
            application = Application(
                name=name,
                description=description,
                api_key=api_key
            )
            
            session.add(application)
            session.commit()
            
            return {
                'id': application.id,
                'name': application.name,
                'description': application.description,
                'api_key': application.api_key,
                'created_at': application.created_at.isoformat() if application.created_at else None
            }
            
        except SQLAlchemyError as e:
            logger.error(f"创建应用失败: {e}")
            session.rollback()
            return None
        finally:
            session.close()
    
    @staticmethod
    def add_model_to_application(application_id: int, model_id: int, is_default: bool = False) -> bool:
        """为应用添加模型授权"""
        session = ModelDBSession()
        try:
            # 如果设置为默认模型，先取消其他默认模型
            if is_default:
                session.query(AppModel).filter_by(
                    application_id=application_id,
                    is_default=True
                ).update({'is_default': False})
            
            # 检查是否已存在
            existing = session.query(AppModel).filter_by(
                application_id=application_id,
                model_id=model_id
            ).first()
            
            if existing:
                existing.is_default = is_default
            else:
                app_model = AppModel(
                    application_id=application_id,
                    model_id=model_id,
                    is_default=is_default
                )
                session.add(app_model)
            
            session.commit()
            return True
            
        except SQLAlchemyError as e:
            logger.error(f"添加模型授权失败: {e}")
            session.rollback()
            return False
        finally:
            session.close()
