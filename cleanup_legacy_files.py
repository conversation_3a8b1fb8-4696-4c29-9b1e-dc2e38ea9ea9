#!/usr/bin/env python3
# cleanup_legacy_files.py - 清理旧版本文件的脚本

import os
import shutil
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def cleanup_legacy_files(dry_run=True):
    """
    清理旧版本的文件和目录
    
    Args:
        dry_run (bool): 如果为True，只显示将要删除的文件，不实际删除
    """
    
    # 可以安全删除的文件和目录（这些在新架构中不再需要）
    files_to_remove = [
        # 备份文件
        "utils/image_converter.py.bak",
        
        # 可能的临时文件
        "*.pyc",
        "*.pyo",
        "*.pyd",
        ".DS_Store",
        "Thumbs.db",
        
        # 旧的日志文件（如果存在）
        "*.log",
        "logs/*.log",
    ]
    
    # 可以清理的缓存目录
    cache_dirs_to_clean = [
        "__pycache__",
        "callbacks/__pycache__",
        "components/__pycache__",
        "styles/__pycache__",
        "utils/__pycache__",
        "fastapi_backend/__pycache__",
        ".pytest_cache",
        ".coverage",
        "htmlcov",
    ]
    
    # 保留但可以选择性删除的文件（用户可以选择）
    optional_files_to_remove = [
        # 这些文件在新架构中不再使用，但可能有参考价值
        # 用户可以选择是否删除
    ]
    
    logger.info("🧹 开始清理旧版本文件...")
    logger.info(f"模式: {'预览模式 (不会实际删除)' if dry_run else '实际删除模式'}")
    logger.info("=" * 50)
    
    removed_count = 0
    
    # 清理缓存目录
    for cache_dir in cache_dirs_to_clean:
        if os.path.exists(cache_dir):
            logger.info(f"{'[预览] ' if dry_run else ''}删除缓存目录: {cache_dir}")
            if not dry_run:
                try:
                    shutil.rmtree(cache_dir)
                    removed_count += 1
                except Exception as e:
                    logger.error(f"删除目录失败 {cache_dir}: {e}")
    
    # 清理指定文件
    import glob
    for pattern in files_to_remove:
        matches = glob.glob(pattern, recursive=True)
        for file_path in matches:
            if os.path.exists(file_path):
                logger.info(f"{'[预览] ' if dry_run else ''}删除文件: {file_path}")
                if not dry_run:
                    try:
                        os.remove(file_path)
                        removed_count += 1
                    except Exception as e:
                        logger.error(f"删除文件失败 {file_path}: {e}")
    
    logger.info("=" * 50)
    if dry_run:
        logger.info("预览完成。要实际执行清理，请运行: python cleanup_legacy_files.py --execute")
    else:
        logger.info(f"清理完成！共删除了 {removed_count} 个文件/目录")
    
    return removed_count


def show_disk_usage():
    """显示磁盘使用情况"""
    logger.info("📊 磁盘使用情况:")
    
    directories_to_check = [
        ".",
        "__pycache__",
        "callbacks/__pycache__",
        "components/__pycache__",
        "styles/__pycache__",
        "utils/__pycache__",
        "fastapi_backend",
        "frontend",
        "assets"
    ]
    
    for directory in directories_to_check:
        if os.path.exists(directory):
            size = get_directory_size(directory)
            logger.info(f"  {directory:<30} {format_size(size)}")


def get_directory_size(directory):
    """获取目录大小"""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(directory):
        for filename in filenames:
            filepath = os.path.join(dirpath, filename)
            try:
                total_size += os.path.getsize(filepath)
            except (OSError, FileNotFoundError):
                pass
    return total_size


def format_size(size_bytes):
    """格式化文件大小"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def backup_important_files():
    """备份重要文件"""
    logger.info("💾 创建重要文件备份...")
    
    backup_dir = "backup_before_cleanup"
    os.makedirs(backup_dir, exist_ok=True)
    
    important_files = [
        "requirements.txt",
        "docker-compose.yml",
        "docker-compose-api.yml",
        ".env",  # 如果存在
        "config/models.json"
    ]
    
    for file_path in important_files:
        if os.path.exists(file_path):
            backup_path = os.path.join(backup_dir, os.path.basename(file_path))
            shutil.copy2(file_path, backup_path)
            logger.info(f"  备份: {file_path} -> {backup_path}")
    
    logger.info(f"备份完成，文件保存在: {backup_dir}/")


def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='清理InspirFlow项目中的旧版本文件')
    parser.add_argument('--execute', action='store_true', 
                       help='实际执行清理（默认只预览）')
    parser.add_argument('--backup', action='store_true', 
                       help='在清理前备份重要文件')
    parser.add_argument('--disk-usage', action='store_true', 
                       help='显示磁盘使用情况')
    
    args = parser.parse_args()
    
    logger.info("🚀 InspirFlow 项目清理工具")
    logger.info("=" * 50)
    
    if args.disk_usage:
        show_disk_usage()
        return
    
    if args.backup:
        backup_important_files()
    
    # 执行清理
    removed_count = cleanup_legacy_files(dry_run=not args.execute)
    
    if not args.execute and removed_count == 0:
        logger.info("✨ 项目已经很干净了！")
    
    logger.info("\n💡 提示:")
    logger.info("- 新的FastAPI后端文件在 fastapi_backend/ 目录")
    logger.info("- 新的前端文件在 frontend/ 目录")
    logger.info("- 原有的Dash应用文件保持不变，可以并行运行")
    logger.info("- 数据库文件和配置文件未被修改")


if __name__ == "__main__":
    main()
