import uuid
import string
import secrets
import json  # 确保导入json模块
import hashlib
import zlib
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy import inspect
from sqlalchemy.exc import SQLAlchemyError
from db_models import (
    Platform, AIModel, User, Conversation, Message, Transaction, Application, AppModel,
    MessageContent, RenderedContent,  # 添加这两个类的导入
    chat_db_engine, model_db_engine, logger, ChatDBSession, ModelDBSession
)
from utils.render_markdown import render_content_to_html
# 创建会话工厂
chat_db_session = ChatDBSession()
model_db_session = ModelDBSession()

def verify_api_key(api_key: str) -> Optional[Dict[str, Any]]:
    """验证API密钥并返回用户数据"""
    session = ChatDBSession()
    try:
        user = session.query(User).filter_by(api_key=api_key, is_active=True).first()
        if not user:
            return None
        # 转换为字典返回
        return get_user_data(user.id)
    except SQLAlchemyError as e:
        logger.error(f"验证API密钥失败: {e}")
        return None
    finally:
        session.close()


# =================== 用户相关操作 ===================

def get_user_data(user_id: int) -> Optional[Dict[str, Any]]:
    """获取用户信息（返回字典而非ORM对象）"""
    session = ChatDBSession()
    try:
        user = session.get(User, user_id)
        if not user:
            return None
        return user.to_dict()
    except SQLAlchemyError as e:
        logger.error(f"获取用户数据失败: {e}")
        return None
    finally:
        session.close()


def get_user_temperature(user_id: int, default: float = 0.7) -> float:
    """获取用户当前温度设置"""
    user_data = get_user_data(user_id)
    if not user_data:
        return default
    return user_data.get("current_temperature", default)


def get_user_model_id(user_id: int) -> Optional[int]:
    """获取用户当前模型ID"""
    user_data = get_user_data(user_id)
    if not user_data:
        return None

    model_id = user_data.get("current_model_id") or user_data.get("default_model_id")
    if not model_id:
        models = get_available_models()
        if models:
            model_id = models[0]["id"]
    return model_id


def update_user_model_preference(user_id: int, model_id: int) -> bool:
    """更新用户的模型偏好设置"""
    session = ChatDBSession()
    try:
        user = session.get(User, user_id)
        if not user:
            return False
        user.current_model_id = model_id
        session.commit()
        return True
    except SQLAlchemyError as e:
        session.rollback()
        logger.error(f"更新用户模型偏好失败: {e}")
        return False
    finally:
        session.close()


def update_user_temperature_preference(user_id: int, temperature: float) -> bool:
    """更新用户的温度设置"""
    session = ChatDBSession()
    try:
        user = session.get(User, user_id)
        if not user:
            return False
        user.current_temperature = temperature
        session.commit()
        return True
    except SQLAlchemyError as e:
        session.rollback()
        logger.error(f"更新用户温度设置失败: {e}")
        return False
    finally:
        session.close()


def update_user_conversation_preference(user_id: int, conversation_id: int) -> bool:
    """更新用户的对话偏好设置"""
    session = ChatDBSession()
    try:
        user = session.get(User, user_id)
        if not user:
            return False
        user.current_conversation_id = conversation_id
        session.commit()
        return True
    except SQLAlchemyError as e:
        session.rollback()
        logger.error(f"更新用户对话偏好失败: {e}")
        return False
    finally:
        session.close()


def update_user_mathjax_preference(user_id: int, use_mathjax: bool) -> bool:
    """更新用户MathJax渲染偏好"""
    session = ChatDBSession()
    try:
        user = session.get(User, user_id)
        if not user:
            return False
        user.mathjax = use_mathjax
        session.commit()
        return True
    except SQLAlchemyError as e:
        session.rollback()
        logger.error(f"更新用户MathJax偏好失败: {e}")
        return False
    finally:
        session.close()


def get_user_statistics(user_id: int) -> Optional[Dict[str, Any]]:
    """获取用户统计数据"""
    chat_session = ChatDBSession()
    model_session = ModelDBSession()
    try:
        user = chat_session.get(User, user_id)
        if not user:
            return None
        conversation_count = chat_session.query(Conversation).filter_by(user_id=user_id).count()
        message_count = chat_session.query(Message).join(Conversation).filter(Conversation.user_id == user_id).count()
        current_model = None
        if user.current_model_id:
            model = model_session.get(AIModel, user.current_model_id)
            if model:
                current_model = model.to_dict()
        return {
            "user_data": get_user_data(user_id),
            "conversation_count": conversation_count,
            "message_count": message_count,
            "current_model": current_model,
            "created_at": user.created_at.isoformat() if user.created_at else None
        }
    except SQLAlchemyError as e:
        logger.error(f"获取用户统计数据失败: {e}")
        return None
    finally:
        chat_session.close()
        model_session.close()


# =================== 平台相关操作 ===================

def get_platforms_list() -> List[Dict[str, Any]]:
    """获取所有平台（字典列表）"""
    session = ModelDBSession()
    try:
        platforms = session.query(Platform).all()
        return [platform.to_dict() for platform in platforms]
    except SQLAlchemyError as e:
        logger.error(f"获取平台列表失败: {e}")
        return []
    finally:
        session.close()


def get_platform_data(platform_id: int) -> Optional[Dict[str, Any]]:
    """根据ID获取平台信息"""
    session = ModelDBSession()
    try:
        platform = session.get(Platform, platform_id)
        if not platform:
            return None
        return platform.to_dict()
    except SQLAlchemyError as e:
        logger.error(f"获取平台信息失败: {e}")
        return None
    finally:
        session.close()


def get_AU_from_model_id(model_id: str) -> Tuple[str, str]:  # 获取model对应的平台的api_key 和 url
    session = ModelDBSession()
    try:
        model = session.query(AIModel).filter_by(id=model_id).first()
        if not model:
            return None, None
        platform = session.query(Platform).filter_by(id=model.platform_id).first()
        if not platform:
            return None, None
        return platform.api_key, platform.base_url
    except SQLAlchemyError as e:
        logger.error(f"获取平台信息失败: {e}")
        return None, None
    finally:
        session.close()


# =================== 模型相关操作 ===================

def get_available_models() -> List[Dict[str, Any]]:
    """获取所有可用模型（字典列表）"""
    session = ModelDBSession()
    try:
        models = session.query(AIModel).all()
        return [model.to_dict() for model in models]
    except SQLAlchemyError as e:
        logger.error(f"获取模型列表失败: {e}")
        return []
    finally:
        session.close()

def get_all_models() -> List[Dict[str, Any]]:
    """获取所有模型（别名函数，保持兼容性）"""
    return get_available_models()

def update_user_model_settings(user_id: int, model_id: int = None, temperature: float = None) -> bool:
    """更新用户模型设置"""
    session = ChatDBSession()
    try:
        user = session.get(User, user_id)
        if not user:
            return False

        if model_id is not None:
            user.current_model_id = model_id
        if temperature is not None:
            user.current_temperature = Decimal(str(temperature))

        session.commit()
        return True
    except SQLAlchemyError as e:
        session.rollback()
        logger.error(f"更新用户模型设置失败: {e}")
        return False
    finally:
        session.close()

def update_user_temperature_preference(user_id: int, temperature: float) -> bool:
    """更新用户温度偏好设置"""
    return update_user_model_settings(user_id, temperature=temperature)


def get_model_data(model_id: int) -> Optional[Dict[str, Any]]:
    """根据ID查找模型"""
    session = ModelDBSession()
    try:
        model = session.get(AIModel, model_id)
        if not model:
            return None
        return model.to_dict()
    except SQLAlchemyError as e:
        logger.error(f"获取模型失败: {e}")
        return None
    finally:
        session.close()


# =================== 应用相关操作 ===================

def get_application_by_api_key(api_key: str) -> Optional[Dict[str, Any]]:
    """通过API密钥获取应用信息"""
    session = ModelDBSession()
    try:
        application = session.query(Application).filter_by(api_key=api_key).first()
        if not application:
            return None
        return application.to_dict()
    except SQLAlchemyError as e:
        logger.error(f"通过API密钥获取应用失败: {e}")
        return None
    finally:
        session.close()


def get_application_models(app_id: int) -> List[Dict[str, Any]]:
    """获取应用可用的模型列表"""
    session = ModelDBSession()
    try:
        app_models = session.query(AppModel).filter_by(application_id=app_id).all()
        result = []
        for app_model in app_models:
            model_data = app_model.model.to_dict()
            model_data['is_default'] = app_model.is_default
            result.append(model_data)
        return result
    except SQLAlchemyError as e:
        logger.error(f"获取应用模型列表失败: {e}")
        return []
    finally:
        session.close()


def get_application_default_model(app_id: int) -> Optional[Dict[str, Any]]:
    """获取应用的默认模型"""
    session = ModelDBSession()
    try:
        app_model = session.query(AppModel).filter_by(application_id=app_id, is_default=True).first()
        if not app_model:
            # 如果没有设置默认模型，返回第一个模型
            app_model = session.query(AppModel).filter_by(application_id=app_id).first()
            if not app_model:
                return None
        return app_model.model.to_dict()
    except SQLAlchemyError as e:
        logger.error(f"获取应用默认模型失败: {e}")
        return None
    finally:
        session.close()


# =================== 对话相关操作 ===================

def get_conversation_data(conversation_id: int) -> Optional[Dict[str, Any]]:
    """获取指定ID的对话数据（字典形式）"""
    session = ChatDBSession()
    try:
        conv = session.get(Conversation, conversation_id)
        if not conv:
            return None
        return conv.to_dict()
    except SQLAlchemyError as e:
        logger.error(f"获取对话失败: {e}")
        return None
    finally:
        session.close()


def get_user_conversations_list(user_id: int) -> List[Dict[str, Any]]:
    """获取用户的所有对话（字典列表）"""
    session = ChatDBSession()
    try:
        conversations = session.query(Conversation).filter_by(user_id=user_id).all()
        return [conv.to_dict() for conv in conversations]
    except SQLAlchemyError as e:
        logger.error(f"获取用户对话列表失败: {e}")
        return []
    finally:
        session.close()


def create_new_conversation(user_id: int, title: str = '新对话') -> Optional[Dict[str, Any]]:
    """创建新对话并返回数据字典"""
    session = ChatDBSession()
    try:
        conversation = Conversation(
            user_id=user_id,
            title=title,
            created_at=datetime.utcnow(),
            latest_revised_at=datetime.utcnow()
        )
        session.add(conversation)
        session.flush()

        # 更新用户当前对话ID
        user = session.get(User, user_id)
        if user:
            user.current_conversation_id = conversation.id

        session.commit()

        return get_conversation_data(conversation.id)
    except SQLAlchemyError as e:
        session.rollback()
        logger.error(f"创建新对话失败: {e}")
        return None
    finally:
        session.close()


def update_conversation_title(conversation_id: int, title: str) -> bool:
    """更新对话标题"""
    session = ChatDBSession()
    try:
        conversation = session.get(Conversation, conversation_id)
        if not conversation:
            return False
        conversation.title = title
        conversation.latest_revised_at = datetime.utcnow()
        session.commit()
        return True
    except SQLAlchemyError as e:
        session.rollback()
        logger.error(f"更新对话标题失败: {e}")
        return False
    finally:
        session.close()


def delete_conversation(conversation_id: int) -> bool:
    """删除指定ID的对话及其所有消息"""
    session = ChatDBSession()
    try:
        conversation = session.query(Conversation).filter_by(id=conversation_id).first()
        if conversation:
            # 检查是否有用户将此对话设为当前对话
            users_with_current = session.query(User).filter_by(current_conversation_id=conversation_id).all()
            for user in users_with_current:
                user.current_conversation_id = None
            session.delete(conversation)
            session.commit()
            logger.info(f"成功删除对话ID: {conversation_id}")
            return True
        else:
            logger.warning(f"未找到对话ID: {conversation_id}")
            return False
    except SQLAlchemyError as e:
        session.rollback()
        logger.error(f"删除对话失败: {e}")
        return False
    finally:
        session.close()


def set_current_conversation(user_id: int, conversation_id: int) -> bool:
    """设置用户的当前对话"""
    session = ChatDBSession()
    try:
        user = session.get(User, user_id)
        if not user:
            return False
        # 验证对话是否存在且属于该用户
        conversation = session.query(Conversation).filter_by(id=conversation_id, user_id=user_id).first()
        if not conversation:
            return False
        user.current_conversation_id = conversation_id
        session.commit()
        return True
    except SQLAlchemyError as e:
        session.rollback()
        logger.error(f"设置当前对话失败: {e}")
        return False
    finally:
        session.close()


# =================== 消息相关操作 ===================

def get_message_data(message_id: int) -> Optional[Dict[str, Any]]:
    """获取指定消息数据（字典形式）"""
    session = ChatDBSession()
    try:
        message = session.get(Message, message_id)
        if not message:
            return None
        return message.to_dict()
    except SQLAlchemyError as e:
        logger.error(f"获取消息数据失败: {e}")
        return None
    finally:
        session.close()


def get_conversation_messages_list(conversation_id: int) -> List[Dict[str, Any]]:
    """获取对话的所有消息（字典列表）"""
    session = ChatDBSession()
    try:
        messages = session.query(Message).filter_by(conversation_id=conversation_id).order_by(Message.created_at).all()
        return [msg.to_dict() for msg in messages]
    except SQLAlchemyError as e:
        logger.error(f"获取对话消息列表失败: {e}")
        return []
    finally:
        session.close()


def load_conversation_messages_by_id(conversation_id: int) -> List[Dict[str, str]]:
    """加载对话历史，转换为AI接口格式"""
    session = ChatDBSession()
    try:
        ins = []
        messages = session.query(Message).filter_by(
            conversation_id=conversation_id, is_error=False).order_by(Message.created_at).all()
        for msg in messages:
            ins.append({"role": msg.role, "content": msg.content})
        # 将数据库中的消息转换为 OpenAI 格式
        return ins
    except SQLAlchemyError as e:
        logger.error(f"加载对话历史失败: {e}")
        return []
    finally:
        session.close()


def update_message_token_count(message_id: int, prompt_tokens: int, completion_tokens: int) -> bool:
    """更新消息的token计数"""
    session = ChatDBSession()
    try:
        message = session.get(Message, message_id)
        if not message:
            return False

        message.prompt_tokens = prompt_tokens
        message.completion_tokens = completion_tokens
        session.commit()
        return True
    except SQLAlchemyError as e:
        session.rollback()
        logger.error(f"更新消息token计数失败: {e}")
        return False
    finally:
        session.close()


def delete_message(message_id: int) -> bool:
    """删除指定ID的单条消息"""
    session = ChatDBSession()
    try:
        message = session.query(Message).filter_by(id=message_id).first()
        if message:
            # 更新对话的最后修改时间
            conversation = session.get(Conversation, message.conversation_id)
            if conversation:
                conversation.latest_revised_at = datetime.utcnow()

            # 删除消息
            session.delete(message)
            session.commit()
            logger.info(f"成功删除消息ID: {message_id}")
            return True
        else:
            logger.warning(f"未找到消息ID: {message_id}")
            return False
    except SQLAlchemyError as e:
        session.rollback()
        logger.error(f"删除消息失败: {e}")
        return False
    finally:
        session.close()


# =================== 账户金额相关 ===================

def deposit_to_user(user_id: int, amount: float, description: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """为用户充值"""
    session = ChatDBSession()
    try:
        user = session.query(User).filter_by(id=user_id).first()
        if not user:
            logger.warning(f"未找到用户ID: {user_id}")
            return None

        # 更新用户余额
        amount_decimal = Decimal(str(amount))
        user.total_deposited += amount_decimal
        user.current_balance += amount_decimal

        # 创建交易记录
        transaction = Transaction(
            user_id=user_id,
            transaction_type='deposit',
            amount=amount_decimal,
            balance_after=user.current_balance,
            description=description or f"充值 ${amount}"
        )

        session.add(transaction)
        session.commit()
        logger.info(f"用户 {user_id} 成功充值 ${amount}，当前余额: ${user.current_balance}")
        return get_user_data(user_id)
    except SQLAlchemyError as e:
        session.rollback()
        logger.error(f"用户充值失败: {e}")
        return None
    finally:
        session.close()


def check_user_balance(user_id: int, model_id: int, estimated_tokens: int = 1000) -> Tuple[bool, str]:
    """检查用户余额是否足够支付预估的token消费"""
    chat_session = ChatDBSession()
    model_session = ModelDBSession()
    try:
        user = chat_session.query(User).filter_by(id=user_id).first()
        model = model_session.query(AIModel).filter_by(id=model_id).first()

        if not user or not model:
            return False, "用户或模型不存在"

        # 计算预估成本 (假设输入输出token数量相等)
        estimated_cost = (Decimal(str(model.input_token_price)) + Decimal(str(model.output_token_price))) * Decimal(
            estimated_tokens) / 1000

        # 检查余额
        if user.current_balance < estimated_cost:
            return False, f"余额不足。当前余额: ${float(user.current_balance)}, 预估成本: ${float(estimated_cost)}"

        return True, "余额充足"
    except SQLAlchemyError as e:
        logger.error(f"检查用户余额失败: {e}")
        return False, f"系统错误: {str(e)}"
    finally:
        chat_session.close()
        model_session.close()


def calculate_message_cost(message_id: int) -> Optional[float]:
    """计算消息成本并更新用户余额"""
    chat_session = ChatDBSession()
    model_session = ModelDBSession()
    try:
        message = chat_session.query(Message).filter_by(id=message_id).first()
        if not message or not message.model_id:
            logger.warning(f"未找到消息ID: {message_id} 或模型ID为空")
            return None

        # 获取关联的模型和用户
        model = model_session.query(AIModel).filter_by(id=message.model_id).first()
        conversation = chat_session.query(Conversation).filter_by(id=message.conversation_id).first()

        if not model or not conversation:
            logger.warning(f"未找到模型或对话")
            return None

        user = chat_session.query(User).filter_by(id=conversation.user_id).first()

        if not user:
            logger.warning(f"未找到用户")
            return None

        # 计算成本 (价格是每1000个token的价格)
        prompt_tokens = message.prompt_tokens or 0
        completion_tokens = message.completion_tokens or 0

        prompt_cost = Decimal(str(model.input_token_price)) * Decimal(prompt_tokens) / 1000
        completion_cost = Decimal(str(model.output_token_price)) * Decimal(completion_tokens) / 1000
        total_cost = prompt_cost + completion_cost

        # 更新消息成本
        message.prompt_cost = prompt_cost
        message.completion_cost = completion_cost
        message.total_cost = total_cost

        # 更新用户统计和余额
        user.total_prompt_tokens += prompt_tokens
        user.total_completion_tokens += completion_tokens
        user.total_spent += total_cost
        user.current_balance -= total_cost

        # 创建交易记录
        transaction = Transaction(
            user_id=user.id,
            transaction_type='consumption',
            amount=total_cost,
            balance_after=user.current_balance,
            message_id=message_id,
            description=f"消息ID {message_id} 消费: 输入 {prompt_tokens} tokens, 输出 {completion_tokens} tokens"
        )

        chat_session.add(transaction)
        chat_session.commit()

        logger.info(f"消息 {message_id} 成本计算完成: ${total_cost}, 用户余额: ${user.current_balance}")
        return float(total_cost)
    except SQLAlchemyError as e:
        chat_session.rollback()
        logger.error(f"计算消息成本失败: {e}")
        return None
    finally:
        chat_session.close()
        model_session.close()


# =================== 事务相关操作 ===================

def get_user_transactions(user_id: int, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """获取用户的交易记录"""
    session = ChatDBSession()
    try:
        transactions = session.query(Transaction).filter_by(user_id=user_id) \
            .order_by(Transaction.created_at.desc()).limit(limit).offset(offset).all()

        return [{
            "id": tx.id,
            "user_id": tx.user_id,
            "transaction_type": tx.transaction_type,
            "amount": float(tx.amount),
            "balance_after": float(tx.balance_after),
            "description": tx.description,
            "message_id": tx.message_id,
            "created_at": tx.created_at.isoformat() if tx.created_at else None
        } for tx in transactions]
    except SQLAlchemyError as e:
        logger.error(f"获取用户交易记录失败: {e}")
        return []
    finally:
        session.close()


# =================== 用户管理相关 ===================

def generate_api_key(prefix: str = "sk-") -> str:
    """生成一个唯一的API密钥"""
    # 生成一个随机的UUID部分
    uuid_part = str(uuid.uuid4()).replace('-', '')

    # 生成一个随机的字符串部分（16个字符）
    alphabet = string.ascii_letters + string.digits
    random_part = ''.join(secrets.choice(alphabet) for _ in range(16))
    # 组合前缀和两个随机部分
    api_key = f"{prefix}{uuid_part}{random_part}"
    return api_key


def create_user(
        permission: int = 1,
        current_model_id: int = 1,
        initial_balance: float = 10.0) -> (
        Tuple)[Optional[Dict[str, Any]], Optional[str]]:
    """创建新用户"""
    session = ChatDBSession()
    try:
        # 生成API密钥
        api_key = generate_api_key()

        # 创建用户
        user = User(
            api_key=api_key,
            created_at=datetime.utcnow(),
            is_active=True,
            permission=permission,

            current_model_id=current_model_id,
            current_temperature=Decimal('0.7'),
            mathjax=False,

            # 余额相关
            total_deposited=Decimal(str(initial_balance)),
            total_spent=Decimal('0.0000'),
            current_balance=Decimal(str(initial_balance)),
            # 统计
            total_prompt_tokens=0,
            total_completion_tokens=0
        )

        session.add(user)
        session.flush()  # 获取自动生成的ID

        # 为用户创建一个初始对话
        title = "欢迎使用AI对话系统" if permission == 9 else "开始使用AI助手"
        conversation = Conversation(
            title=title,
            user_id=user.id,
            created_at=datetime.utcnow(),
            latest_revised_at=datetime.utcnow()
        )

        session.add(conversation)
        session.flush()

        # 更新用户的当前对话
        user.current_conversation_id = conversation.id

        # 如果是管理员，添加初始消息
        if permission == 9:
            welcome_message = Message(
                conversation_id=conversation.id,
                role="assistant",
                content="欢迎使用AI对话系统！我已准备好回答您的问题。",
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                model_id=current_model_id,
                temperature=Decimal('0.7'),
                prompt_tokens=0,
                completion_tokens=0,
                prompt_cost=Decimal('0.000000'),
                completion_cost=Decimal('0.000000'),
                total_cost=Decimal('0.000000')
            )
            session.add(welcome_message)

        # 添加用户的充值交易记录
        deposit = Transaction(
            user_id=user.id,
            transaction_type='deposit',
            amount=Decimal(str(initial_balance)),
            balance_after=Decimal(str(initial_balance)),
            description="初始账户充值",
            created_at=datetime.utcnow()
        )

        session.add(deposit)
        session.commit()

        logger.info(f"用户已创建，权限级别: {permission}, API密钥: {api_key}")

        # 返回用户数据和API密钥
        return get_user_data(user.id), api_key

    except SQLAlchemyError as e:
        session.rollback()
        logger.error(f"创建用户失败: {e}")
        return None, None
    finally:
        session.close()


# =================== 管理员功能 ===================

def is_admin_user(user_id: int) -> bool:
    """检查用户是否具有管理员权限 (permission=9)"""
    session = ChatDBSession()
    try:
        user = session.get(User, user_id)
        return user is not None and user.permission == 9
    except SQLAlchemyError as e:
        logger.error(f"检查管理员权限失败: {e}")
        return False
    finally:
        session.close()


def get_all_users(admin_id: int) -> Optional[List[Dict[str, Any]]]:
    """获取所有用户列表（仅限管理员）"""
    if not is_admin_user(admin_id):
        logger.warning(f"非管理员用户 {admin_id} 尝试访问用户列表")
        return None

    session = ChatDBSession()
    try:
        users = session.query(User).all()
        return [{
            "id": user.id,
            "api_key": user.api_key[:10] + "..." + user.api_key[-5:],  # 只返回部分API密钥
            "permission": user.permission,
            "is_active": user.is_active,
            "current_balance": float(user.current_balance) if user.current_balance else 0.0,
            "total_spent": float(user.total_spent) if user.total_spent else 0.0,
            "total_deposited": float(user.total_deposited) if user.total_deposited else 0.0,
            "created_at": user.created_at.isoformat() if user.created_at else None,
        } for user in users]
    except SQLAlchemyError as e:
        logger.error(f"获取用户列表失败: {e}")
        return None
    finally:
        session.close()


def toggle_user_active_status(admin_id: int, user_id: int) -> bool:
    """切换用户活跃状态（启用/禁用用户）(仅限管理员)"""
    if not is_admin_user(admin_id):
        logger.warning(f"非管理员用户 {admin_id} 尝试切换用户 {user_id} 的活跃状态")
        return False

    session = ChatDBSession()
    try:
        user = session.get(User, user_id)
        if not user:
            return False

        # 不允许禁用自己（管理员）
        if user.id == admin_id:
            logger.warning(f"管理员 {admin_id} 尝试禁用自己的账户")
            return False

        # 切换状态
        user.is_active = not user.is_active
        session.commit()
        return True
    except SQLAlchemyError as e:
        session.rollback()
        logger.error(f"切换用户活跃状态失败: {e}")
        return False
    finally:
        session.close()


def admin_add_user_balance(admin_id: int, user_id: int, amount: float, description: str = None) -> bool:
    """管理员为用户充值（仅限管理员）"""
    if not is_admin_user(admin_id):
        logger.warning(f"非管理员用户 {admin_id} 尝试为用户 {user_id} 充值")
        return False

    if amount <= 0:
        return False

    session = ChatDBSession()
    try:
        user = session.get(User, user_id)
        if not user:
            return False

        # 更新用户余额
        amount_decimal = Decimal(str(amount))
        user.total_deposited += amount_decimal
        user.current_balance += amount_decimal

        # 创建交易记录
        transaction = Transaction(
            user_id=user_id,
            transaction_type='admin_deposit',
            amount=amount_decimal,
            balance_after=user.current_balance,
            description=description or f"管理员充值: ${amount}"
        )

        session.add(transaction)
        session.commit()
        logger.info(f"管理员 {admin_id} 为用户 {user_id} 充值 ${amount}，用户当前余额: ${user.current_balance}")
        return True
    except SQLAlchemyError as e:
        session.rollback()
        logger.error(f"管理员充值失败: {e}")
        return False
    finally:
        session.close()


def admin_create_new_user(
        admin_id: int,
        permission: int = 1,
        initial_balance: float = 10.0) \
        -> Optional[Tuple[Dict[str, Any], str]]:
    """管理员创建新用户（仅限管理员）"""
    if not is_admin_user(admin_id):
        logger.warning(f"非管理员用户 {admin_id} 尝试创建用户")
        return None

    # 使用现有的create_user函数和默认模型
    return create_user(permission=permission, initial_balance=initial_balance)



# 在 db_operator.py 中添加和修改以下函数

import hashlib
import zlib
from utils.render_markdown import render_content_to_html

def get_content_hash(content):
    """计算内容的哈希值"""
    return hashlib.sha256(content.encode('utf-8')).hexdigest()

def get_message_content(message_id):
    """获取消息内容"""
    session = ChatDBSession()
    try:
        content_obj = session.query(MessageContent).filter_by(message_id=message_id).first()
        if content_obj:
            return content_obj.content
        return None
    except Exception as e:
        logger.error(f"获取消息内容失败: {e}")
        return None
    finally:
        session.close()

def update_message_content(message_id, new_content, is_error=False, error_info=None):
    """更新消息内容"""
    session = ChatDBSession()
    try:
        # 获取消息
        message = session.get(Message, message_id)
        if not message:
            logger.warning(f"未找到消息ID: {message_id}")
            return None

        # 计算内容哈希
        content_hash = get_content_hash(new_content)

        # 更新消息内容
        content_obj = session.query(MessageContent).filter_by(message_id=message_id).first()
        if content_obj:
            content_obj.content = new_content
            content_obj.content_hash = content_hash
            content_obj.updated_at = datetime.utcnow()
        else:
            content_obj = MessageContent(
                message_id=message_id,
                content=new_content,
                content_hash=content_hash
            )
            session.add(content_obj)

        # 更新消息状态
        message.updated_at = datetime.utcnow()
        message.is_error = is_error
        if error_info is not None:
            message.error_info = error_info

        # 更新对话的最后修改时间
        conversation = session.get(Conversation, message.conversation_id)
        if conversation:
            conversation.latest_revised_at = datetime.utcnow()

        # 清除渲染缓存
        rendered = session.query(RenderedContent).filter_by(message_id=message_id).first()
        if rendered:
            session.delete(rendered)

        session.commit()

        # 返回更新后的消息数据
        return get_message_data(message_id)
    except Exception as e:
        session.rollback()
        logger.error(f"更新消息内容失败: {e}")
        return None
    finally:
        session.close()

def create_message(conversation_id, role, content,
                  model_id=None, temperature=None,
                  max_tokens=None, is_error=False,
                  error_info=None,
                  prompt_tokens=None,
                  completion_tokens=None):
    """创建新消息"""
    session = ChatDBSession()
    try:
        # 更新对话的最后修改时间
        conversation = session.get(Conversation, conversation_id)
        if not conversation:
            return None
        conversation.latest_revised_at = datetime.utcnow()

        # 创建消息
        message = Message(
            conversation_id=conversation_id,
            role=role,
            model_id=model_id,
            temperature=Decimal(str(temperature)) if temperature is not None else None,
            max_tokens=max_tokens,
            is_error=is_error,
            error_info=error_info,
            prompt_tokens=prompt_tokens,
            completion_tokens=completion_tokens,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        session.add(message)
        session.flush()  # 获取消息ID

        # 计算内容哈希
        content_hash = get_content_hash(content)

        # 创建消息内容
        message_content = MessageContent(
            message_id=message.id,
            content=content,
            content_hash=content_hash,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        session.add(message_content)

        session.commit()
        session.refresh(message)

        return get_message_data(message.id)
    except Exception as e:
        session.rollback()
        logger.error(f"创建消息失败: {e}")
        return None
    finally:
        session.close()

def get_message_data(message_id):
    """获取消息数据（包括内容）"""
    session = ChatDBSession()
    try:
        message = session.get(Message, message_id)
        if not message:
            return None

        # 获取消息字典
        message_dict = message.to_dict()

        # 如果没有内容，尝试从 MessageContent 表获取
        if not message_dict.get('content'):
            content_obj = session.query(MessageContent).filter_by(message_id=message_id).first()
            if content_obj:
                message_dict['content'] = content_obj.content

        return message_dict
    except Exception as e:
        logger.error(f"获取消息数据失败: {e}")
        return None
    finally:
        session.close()

def get_conversation_messages_list(conversation_id):
    """获取对话的所有消息"""
    session = ChatDBSession()
    try:
        # 使用联结查询同时获取消息和内容
        messages = session.query(Message).filter_by(conversation_id=conversation_id).order_by(Message.created_at).all()

        result = []
        for msg in messages:
            msg_dict = msg.to_dict()
            # 如果没有内容，尝试从 MessageContent 表获取
            if not msg_dict.get('content'):
                content_obj = session.query(MessageContent).filter_by(message_id=msg.id).first()
                if content_obj:
                    msg_dict['content'] = content_obj.content
            result.append(msg_dict)

        return result
    except Exception as e:
        logger.error(f"获取对话消息列表失败: {e}")
        return []
    finally:
        session.close()

def load_conversation_messages_by_id(conversation_id):
    """加载对话历史，转换为AI接口格式"""
    session = ChatDBSession()
    try:
        messages = session.query(Message).filter_by(
            conversation_id=conversation_id, is_error=False).order_by(Message.created_at).all()

        result = []
        for msg in messages:
            # 获取消息内容
            content_obj = session.query(MessageContent).filter_by(message_id=msg.id).first()
            content = content_obj.content if content_obj else ""

            result.append({"role": msg.role, "content": content})

        return result
    except Exception as e:
        logger.error(f"加载对话历史失败: {e}")
        return []
    finally:
        session.close()

def delete_message(message_id):
    """删除消息"""
    session = ChatDBSession()
    try:
        message = session.get(Message, message_id)
        if not message:
            return False

        # 更新对话的最后修改时间
        conversation = session.get(Conversation, message.conversation_id)
        if conversation:
            conversation.latest_revised_at = datetime.utcnow()

        # 删除消息（关联的内容和渲染缓存会通过级联删除）
        session.delete(message)
        session.commit()

        return True
    except Exception as e:
        session.rollback()
        logger.error(f"删除消息失败: {e}")
        return False
    finally:
        session.close()

# 渲染内容相关函数
def store_rendered_content(message_id, content):
    """存储消息的渲染内容"""
    session = ChatDBSession()
    try:
        # 检查消息是否存在
        message = session.get(Message, message_id)
        if not message:
            logger.warning(f"未找到消息ID: {message_id}")
            return False

        # 渲染内容（带MathJax和不带MathJax）
        html_with_mathjax = render_content_to_html(content, detect_markdown=True, mathjax=True)
        html_without_mathjax = render_content_to_html(content, detect_markdown=True, mathjax=False)

        # 压缩HTML内容以节省空间
        compressed_with_mathjax = zlib.compress(html_with_mathjax.encode('utf-8'))
        compressed_without_mathjax = zlib.compress(html_without_mathjax.encode('utf-8'))

        # 检查是否已存在渲染内容
        rendered = session.query(RenderedContent).filter_by(message_id=message_id).first()

        if rendered:
            # 更新现有记录
            rendered.rendered_with_mathjax = compressed_with_mathjax
            rendered.rendered_without_mathjax = compressed_without_mathjax
            rendered.updated_at = datetime.utcnow()
        else:
            # 创建新记录
            rendered = RenderedContent(
                message_id=message_id,
                rendered_with_mathjax=compressed_with_mathjax,
                rendered_without_mathjax=compressed_without_mathjax
            )
            session.add(rendered)

        session.commit()
        return True
    except Exception as e:
        session.rollback()
        logger.error(f"存储渲染内容失败: {e}")
        return False
    finally:
        session.close()

def get_rendered_content(message_id, mathjax=True):
    """获取消息的渲染内容"""
    session = ChatDBSession()
    try:
        rendered = session.query(RenderedContent).filter_by(message_id=message_id).first()
        if not rendered:
            return None

        # 选择适当的渲染内容
        compressed_content = rendered.rendered_with_mathjax if mathjax else rendered.rendered_without_mathjax

        # 解压缩内容
        if compressed_content:
            return zlib.decompress(compressed_content).decode('utf-8')
        return None
    except Exception as e:
        logger.error(f"获取渲染内容失败: {e}")
        return None
    finally:
        session.close()

def clear_rendered_content(message_id):
    """清除消息的渲染缓存"""
    session = ChatDBSession()
    try:
        rendered = session.query(RenderedContent).filter_by(message_id=message_id).first()
        if rendered:
            session.delete(rendered)
            session.commit()
            return True
        return False
    except Exception as e:
        session.rollback()
        logger.error(f"清除渲染缓存失败: {e}")
        return False
    finally:
        session.close()
