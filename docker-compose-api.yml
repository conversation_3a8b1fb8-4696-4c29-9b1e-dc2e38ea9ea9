services:
  # Web应用服务 (原有的Dash应用)
  web:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8050:8050"
    volumes:
      - /volume-append/Inspirflow-database:/app/database
    environment:
      - DATABASE_PATH=/app/database/chat_system.db
    command: ["gunicorn", "--bind", "0.0.0.0:8050", "--workers", "4", "--timeout", "300", "wsgi:server"]
    networks:
      - inspirflow-network

  # API服务 (新的Flask API)
  api-service:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5002:5002"
    volumes:
      - /volume-append/Inspirflow-database:/app/database
    environment:
      - DATABASE_PATH=/app/database/chat_system.db
      - MARIADB_USER=${MARIADB_USER:-root}
      - MARIADB_PASSWORD=${MARIADB_PASSWORD:-rw80827}
      - MARIADB_HOST=${MARIADB_HOST:-*************}
      - MARIADB_PORT=${MARIADB_PORT:-3306}
      - MARIADB_MODEL_DB=${MARIADB_MODEL_DB:-model_registry}
      - MARIADB_CHAT_DB=${MARIADB_CHAT_DB:-chat_system}
    command: ["gunicorn", "--bind", "0.0.0.0:5002", "--workers", "4", "--timeout", "300", "api_wsgi:application"]
    networks:
      - inspirflow-network
    depends_on:
      - web

networks:
  inspirflow-network:
    driver: bridge
