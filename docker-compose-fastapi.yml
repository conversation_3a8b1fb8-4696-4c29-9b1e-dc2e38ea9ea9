# docker-compose-fastapi.yml - FastAPI后端Docker配置
version: '3.8'

services:
  # FastAPI后端服务
  fastapi-backend:
    build:
      context: .
      dockerfile: Dockerfile.fastapi
    ports:
      - "8000:8000"
    environment:
      - MARIADB_HOST=${MARIADB_HOST:-*************}
      - MARIADB_PORT=${MARIADB_PORT:-3306}
      - MARIADB_USER=${MARIADB_USER:-root}
      - MARIADB_PASSWORD=${MARIADB_PASSWORD:-rw80827}
      - MARIADB_MODEL_DB=${MARIADB_MODEL_DB:-model_registry}
      - MARIADB_CHAT_DB=${MARIADB_CHAT_DB:-chat_system}
    volumes:
      - ./frontend:/app/frontend
      - ./fastapi_backend:/app/fastapi_backend
    command: ["uvicorn", "fastapi_backend.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
    networks:
      - inspirflow-network
    depends_on:
      - mariadb
    restart: unless-stopped

  # 原有的Dash应用（可选，用于对比测试）
  dash-app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8050:8050"
    environment:
      - MARIADB_HOST=${MARIADB_HOST:-*************}
      - MARIADB_PORT=${MARIADB_PORT:-3306}
      - MARIADB_USER=${MARIADB_USER:-root}
      - MARIADB_PASSWORD=${MARIADB_PASSWORD:-rw80827}
      - MARIADB_MODEL_DB=${MARIADB_MODEL_DB:-model_registry}
      - MARIADB_CHAT_DB=${MARIADB_CHAT_DB:-chat_system}
    command: ["gunicorn", "--bind", "0.0.0.0:8050", "--workers", "4", "--timeout", "300", "wsgi:server"]
    networks:
      - inspirflow-network
    depends_on:
      - mariadb
    restart: unless-stopped
    profiles:
      - legacy  # 使用profile来控制是否启动

  # API服务（原有的，运行在5002端口）
  api-service:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5002:5002"
    environment:
      - MARIADB_HOST=${MARIADB_HOST:-*************}
      - MARIADB_PORT=${MARIADB_PORT:-3306}
      - MARIADB_USER=${MARIADB_USER:-root}
      - MARIADB_PASSWORD=${MARIADB_PASSWORD:-rw80827}
      - MARIADB_MODEL_DB=${MARIADB_MODEL_DB:-model_registry}
      - MARIADB_CHAT_DB=${MARIADB_CHAT_DB:-chat_system}
    command: ["gunicorn", "--bind", "0.0.0.0:5002", "--workers", "4", "--timeout", "300", "api_wsgi:application"]
    networks:
      - inspirflow-network
    depends_on:
      - mariadb
    restart: unless-stopped

  # MariaDB数据库
  mariadb:
    image: mariadb:10.6
    environment:
      - MYSQL_ROOT_PASSWORD=${MARIADB_PASSWORD:-rw80827}
      - MYSQL_DATABASE=${MARIADB_CHAT_DB:-chat_system}
    ports:
      - "3306:3306"
    volumes:
      - mariadb_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - inspirflow-network
    restart: unless-stopped
    profiles:
      - db  # 仅在需要本地数据库时启动

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    networks:
      - inspirflow-network
    depends_on:
      - fastapi-backend
    restart: unless-stopped
    profiles:
      - proxy  # 仅在需要代理时启动

networks:
  inspirflow-network:
    driver: bridge

volumes:
  mariadb_data:
    driver: local
