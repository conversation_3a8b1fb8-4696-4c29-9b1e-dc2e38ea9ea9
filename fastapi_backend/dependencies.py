# fastapi_backend/dependencies.py - FastAPI依赖项
import os
import sys
import logging
from typing import Optional
from fastapi import HTTPException, Depends, status
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from utils.auth import verify_access_token
from db_operator import get_user_data

logger = logging.getLogger(__name__)

# HTTP Bearer认证
security = HTTPBearer()


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """获取当前用户信息"""
    try:
        # 验证JWT令牌
        api_key = verify_access_token(credentials.credentials)
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的访问令牌",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 获取用户数据
        user_data = get_user_data_by_api_key(api_key)
        if not user_data:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在或已被禁用",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 添加API密钥到用户数据中（用于代理请求）
        user_data["api_key"] = api_key

        return user_data
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取当前用户失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="认证失败",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_admin_user(current_user: dict = Depends(get_current_user)):
    """获取管理员用户（权限检查）"""
    if current_user.get("permission") != 9:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user


def get_user_data_by_api_key(api_key: str) -> Optional[dict]:
    """通过API密钥获取用户数据"""
    try:
        from db_operator import verify_api_key
        return verify_api_key(api_key)
    except Exception as e:
        logger.error(f"通过API密钥获取用户数据失败: {e}")
        return None


class PermissionChecker:
    """权限检查器"""
    
    def __init__(self, required_permission: int):
        self.required_permission = required_permission
    
    def __call__(self, current_user: dict = Depends(get_current_user)):
        if current_user.get("permission", 0) < self.required_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"需要权限级别 {self.required_permission} 或更高"
            )
        return current_user


# 常用权限检查器实例
require_admin = PermissionChecker(9)
require_user = PermissionChecker(1)
