# fastapi_backend/main.py - 新的FastAPI后端主文件
import os
import sys
import logging
from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from contextlib import asynccontextmanager

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

# 导入路由模块
from fastapi_backend.routes import auth, conversations, messages, models, admin, users, chat

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("InspirFlow FastAPI 后端启动中...")
    yield
    logger.info("InspirFlow FastAPI 后端关闭中...")


# 创建FastAPI应用
app = FastAPI(
    title="InspirFlow API",
    description="InspirFlow 聊天应用的后端API",
    version="2.0.0",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制为特定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(auth.router, prefix="/api/v1/auth", tags=["认证"])
app.include_router(users.router, prefix="/api/v1/users", tags=["用户管理"])
app.include_router(conversations.router, prefix="/api/v1/conversations", tags=["对话管理"])
app.include_router(messages.router, prefix="/api/v1/messages", tags=["消息管理"])
app.include_router(models.router, prefix="/api/v1/models", tags=["模型管理"])
app.include_router(admin.router, prefix="/api/v1/admin", tags=["管理员功能"])
app.include_router(chat.router, prefix="/api/v1/chat", tags=["聊天功能"])

# 挂载静态文件
app.mount("/static", StaticFiles(directory="frontend/static"), name="static")

# 根路径返回前端页面
@app.get("/")
async def read_root():
    """返回前端主页"""
    return FileResponse("frontend/index.html")

# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查"""
    from fastapi_backend.services.external_api import external_api_service

    # 检查外部服务状态
    services_health = await external_api_service.check_services_health()

    return {
        "status": "healthy",
        "message": "InspirFlow API is running",
        "services": services_health,
        "timestamp": int(time.time())
    }

# 错误处理
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    logger.error(f"HTTP异常: {exc.status_code} - {exc.detail}")
    return {"error": exc.detail, "status_code": exc.status_code}

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    logger.error(f"未处理的异常: {str(exc)}")
    return {"error": "内部服务器错误", "status_code": 500}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
