# fastapi_backend/models/schemas.py - Pydantic模型定义
from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, Field


# ========== 认证相关模型 ==========
class LoginRequest(BaseModel):
    api_key: str = Field(..., description="用户API密钥")


class LoginResponse(BaseModel):
    access_token: str = Field(..., description="JWT访问令牌")
    user_id: int = Field(..., description="用户ID")
    permission: int = Field(..., description="用户权限级别")
    is_admin: bool = Field(..., description="是否为管理员")


# ========== 用户相关模型 ==========
class UserInfo(BaseModel):
    id: int
    api_key: str
    created_at: datetime
    is_active: bool
    permission: int
    mathjax: bool
    current_model_id: Optional[int]
    current_temperature: Optional[Decimal]
    current_conversation_id: Optional[int]
    total_deposited: Decimal
    total_spent: Decimal
    current_balance: Decimal
    total_prompt_tokens: int
    total_completion_tokens: int


class UserStats(BaseModel):
    total_conversations: int
    total_messages: int
    total_tokens: int
    current_balance: float


class CreateUserRequest(BaseModel):
    permission: int = Field(default=1, description="用户权限级别")
    initial_balance: float = Field(default=10.0, description="初始余额")


class CreateUserResponse(BaseModel):
    user: UserInfo
    api_key: str


class UpdateUserBalanceRequest(BaseModel):
    user_id: int
    amount: float
    description: Optional[str] = None


# ========== 对话相关模型 ==========
class ConversationInfo(BaseModel):
    id: int
    title: Optional[str]
    created_at: datetime
    latest_revised_at: datetime
    user_id: int
    message_count: Optional[int] = 0


class CreateConversationResponse(BaseModel):
    conversation: ConversationInfo


class ConversationListResponse(BaseModel):
    conversations: List[ConversationInfo]


# ========== 消息相关模型 ==========
class MessageInfo(BaseModel):
    id: int
    conversation_id: int
    role: str
    content: str
    model_id: Optional[int]
    temperature: Optional[Decimal]
    max_tokens: Optional[int]
    is_error: bool
    error_info: Optional[str]
    prompt_tokens: Optional[int]
    completion_tokens: Optional[int]
    prompt_cost: Optional[Decimal]
    completion_cost: Optional[Decimal]
    total_cost: Optional[Decimal]
    created_at: datetime
    updated_at: datetime


class SendMessageRequest(BaseModel):
    conversation_id: int
    content: str
    images: Optional[List[str]] = Field(default=[], description="图片URL列表")
    model_id: Optional[int] = None
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None


class UpdateMessageRequest(BaseModel):
    content: str


class MessageListResponse(BaseModel):
    messages: List[MessageInfo]


# ========== 模型相关模型 ==========
class ModelInfo(BaseModel):
    id: int
    display_name: str
    internal_name: str
    platform_id: int
    platform_name: str
    is_visible_model: bool
    input_token_price: Decimal
    output_token_price: Decimal
    free: bool
    created_at: datetime


class ModelListResponse(BaseModel):
    models: List[ModelInfo]


class UserModelSettings(BaseModel):
    current_model_id: int
    current_temperature: float


# ========== 聊天相关模型 ==========
class ChatMessage(BaseModel):
    role: str
    content: str


class ChatRequest(BaseModel):
    messages: List[ChatMessage]
    model: Optional[str] = None
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    stream: bool = False


class ChatResponse(BaseModel):
    id: str
    object: str
    created: int
    model: str
    choices: List[Dict[str, Any]]
    usage: Optional[Dict[str, int]] = None


# ========== 管理员相关模型 ==========
class AdminUserInfo(BaseModel):
    id: int
    api_key: str
    created_at: datetime
    is_active: bool
    permission: int
    current_balance: Decimal
    total_spent: Decimal
    total_conversations: int
    total_messages: int


class AdminUserListResponse(BaseModel):
    users: List[AdminUserInfo]


class ToggleUserStatusRequest(BaseModel):
    user_id: int


# ========== 通用响应模型 ==========
class SuccessResponse(BaseModel):
    success: bool = True
    message: str = "操作成功"


class ErrorResponse(BaseModel):
    success: bool = False
    error: str
    detail: Optional[str] = None


# ========== 设置相关模型 ==========
class UpdateSettingsRequest(BaseModel):
    mathjax: Optional[bool] = None
    current_model_id: Optional[int] = None
    current_temperature: Optional[float] = None
