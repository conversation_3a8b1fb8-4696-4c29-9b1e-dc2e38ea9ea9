# fastapi_backend/routes/admin.py - 管理员功能路由
import os
import sys
import logging
from fastapi import APIRouter, HTTPException, Depends, status

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from fastapi_backend.models.schemas import (
    AdminUserInfo, AdminUserListResponse, CreateUserRequest, CreateUserResponse,
    UpdateUserBalanceRequest, ToggleUserStatusRequest, SuccessResponse
)
from fastapi_backend.dependencies import get_admin_user
from db_operator import (
    get_all_users, admin_create_new_user, admin_add_user_balance,
    toggle_user_active_status, get_user_data
)

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/users", response_model=AdminUserListResponse)
async def get_all_users_admin(admin_user: dict = Depends(get_admin_user)):
    """获取所有用户列表（管理员）"""
    try:
        users_data = get_all_users()
        users = [AdminUserInfo(**user) for user in users_data]
        
        return AdminUserListResponse(users=users)
    
    except Exception as e:
        logger.error(f"获取用户列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户列表失败"
        )


@router.post("/users", response_model=CreateUserResponse)
async def create_user_admin(
    request: CreateUserRequest,
    admin_user: dict = Depends(get_admin_user)
):
    """创建新用户（管理员）"""
    try:
        result = admin_create_new_user(
            admin_user["id"], 
            request.permission, 
            request.initial_balance
        )
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="创建用户失败"
            )
        
        user_data, api_key = result
        
        return CreateUserResponse(
            user=AdminUserInfo(**user_data),
            api_key=api_key
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建用户失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建用户失败"
        )


@router.post("/users/balance", response_model=SuccessResponse)
async def update_user_balance_admin(
    request: UpdateUserBalanceRequest,
    admin_user: dict = Depends(get_admin_user)
):
    """更新用户余额（管理员）"""
    try:
        # 检查目标用户是否存在
        target_user = get_user_data(request.user_id)
        if not target_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="目标用户不存在"
            )
        
        # 更新余额
        success = admin_add_user_balance(
            admin_user["id"],
            request.user_id,
            request.amount,
            request.description or "管理员充值"
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="更新用户余额失败"
            )
        
        return SuccessResponse(message="用户余额更新成功")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新用户余额失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新用户余额失败"
        )


@router.post("/users/toggle-status", response_model=SuccessResponse)
async def toggle_user_status_admin(
    request: ToggleUserStatusRequest,
    admin_user: dict = Depends(get_admin_user)
):
    """切换用户激活状态（管理员）"""
    try:
        # 检查目标用户是否存在
        target_user = get_user_data(request.user_id)
        if not target_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="目标用户不存在"
            )
        
        # 不允许管理员禁用自己
        if request.user_id == admin_user["id"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能禁用自己的账户"
            )
        
        # 切换用户状态
        success = toggle_user_active_status(admin_user["id"], request.user_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="切换用户状态失败"
            )
        
        # 获取更新后的状态
        updated_user = get_user_data(request.user_id)
        status_text = "激活" if updated_user["is_active"] else "禁用"
        
        return SuccessResponse(message=f"用户已{status_text}")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"切换用户状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="切换用户状态失败"
        )


@router.get("/users/{user_id}", response_model=AdminUserInfo)
async def get_user_admin(
    user_id: int,
    admin_user: dict = Depends(get_admin_user)
):
    """获取指定用户信息（管理员）"""
    try:
        user_data = get_user_data(user_id)
        if not user_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        return AdminUserInfo(**user_data)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户信息失败"
        )
