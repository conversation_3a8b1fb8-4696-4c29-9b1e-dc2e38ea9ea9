# fastapi_backend/routes/auth.py - 认证相关路由
import os
import sys
import logging
from fastapi import APIRouter, HTTPException, status

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from fastapi_backend.models.schemas import LoginRequest, LoginResponse
from db_operator import verify_api_key
from utils.auth import create_access_token

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/login", response_model=LoginResponse)
async def login(request: LoginRequest):
    """用户登录"""
    try:
        # 验证API密钥
        user_data = verify_api_key(request.api_key)
        if not user_data:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的API密钥"
            )
        
        # 创建JWT令牌
        access_token = create_access_token(request.api_key)
        
        return LoginResponse(
            access_token=access_token,
            user_id=user_data["id"],
            permission=user_data["permission"],
            is_admin=user_data["permission"] == 9
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"登录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录过程中发生错误"
        )


@router.post("/verify")
async def verify_token(request: LoginRequest):
    """验证API密钥（不返回令牌）"""
    try:
        user_data = verify_api_key(request.api_key)
        if not user_data:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的API密钥"
            )
        
        return {
            "valid": True,
            "user_id": user_data["id"],
            "permission": user_data["permission"],
            "is_admin": user_data["permission"] == 9
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"验证API密钥失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="验证过程中发生错误"
        )
