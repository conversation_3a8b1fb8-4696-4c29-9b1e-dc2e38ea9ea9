# fastapi_backend/routes/chat.py - 聊天功能路由（兼容OpenAI API）
import os
import sys
import json
import time
import logging
from typing import AsyncGenerator
from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.responses import StreamingResponse

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from fastapi_backend.models.schemas import ChatRequest, ChatResponse
from fastapi_backend.dependencies import get_current_user
from fastapi_backend.services.external_api import external_api_service
from db_operator import get_user_model_id, get_user_temperature, get_model_data
from openaiSDK import OpenAISDK

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/completions")
async def chat_completions(
    request: ChatRequest,
    current_user: dict = Depends(get_current_user)
):
    """聊天完成接口（兼容OpenAI API）"""
    try:
        # 获取用户的API密钥（用于代理到聊天服务）
        user_api_key = current_user.get("api_key")
        if not user_api_key:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户API密钥不可用"
            )

        # 准备请求数据
        request_data = {
            "messages": [{"role": msg.role, "content": msg.content} for msg in request.messages],
            "model": request.model,
            "temperature": request.temperature,
            "max_tokens": request.max_tokens,
            "stream": request.stream
        }

        if request.stream:
            # 流式响应 - 代理到聊天服务
            return StreamingResponse(
                external_api_service.proxy_chat_stream_request(user_api_key, request_data),
                media_type="text/plain"
            )
        else:
            # 非流式响应 - 代理到聊天服务
            response = await external_api_service.proxy_chat_request(user_api_key, request_data)
            return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"聊天完成失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="聊天完成失败"
        )


async def stream_chat_response(
    sdk: OpenAISDK,
    messages: list,
    model_data: dict,
    temperature: float,
    max_tokens: int = None
) -> AsyncGenerator[str, None]:
    """流式聊天响应生成器"""
    try:
        # 生成流式响应
        for chunk in sdk.generate_stream_response(
            messages=messages,
            model_data=model_data,
            temperature=temperature,
            max_tokens=max_tokens
        ):
            if chunk:
                # 构造OpenAI兼容的流式响应格式
                stream_chunk = {
                    "id": f"chatcmpl-{int(time.time())}",
                    "object": "chat.completion.chunk",
                    "created": int(time.time()),
                    "model": model_data["display_name"],
                    "choices": [
                        {
                            "index": 0,
                            "delta": {
                                "content": chunk.get("content", "")
                            },
                            "finish_reason": None
                        }
                    ]
                }
                
                yield f"data: {json.dumps(stream_chunk, ensure_ascii=False)}\n\n"
        
        # 发送结束标记
        end_chunk = {
            "id": f"chatcmpl-{int(time.time())}",
            "object": "chat.completion.chunk",
            "created": int(time.time()),
            "model": model_data["display_name"],
            "choices": [
                {
                    "index": 0,
                    "delta": {},
                    "finish_reason": "stop"
                }
            ]
        }
        
        yield f"data: {json.dumps(end_chunk, ensure_ascii=False)}\n\n"
        yield "data: [DONE]\n\n"
        
    except Exception as e:
        logger.error(f"流式响应生成失败: {e}")
        error_chunk = {
            "error": {
                "message": "生成回复时发生错误",
                "type": "server_error"
            }
        }
        yield f"data: {json.dumps(error_chunk, ensure_ascii=False)}\n\n"


@router.get("/models")
async def list_models(current_user: dict = Depends(get_current_user)):
    """列出可用模型（兼容OpenAI API）"""
    try:
        from db_operator import get_all_models
        
        models_data = get_all_models()
        
        # 转换为OpenAI兼容格式
        models = []
        for model in models_data:
            models.append({
                "id": model["internal_name"],
                "object": "model",
                "created": int(time.mktime(model["created_at"].timetuple())),
                "owned_by": model["platform_name"],
                "permission": [],
                "root": model["internal_name"],
                "parent": None
            })
        
        return {
            "object": "list",
            "data": models
        }
    
    except Exception as e:
        logger.error(f"获取模型列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取模型列表失败"
        )
