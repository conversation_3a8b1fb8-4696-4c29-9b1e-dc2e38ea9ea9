# fastapi_backend/routes/conversations.py - 对话管理路由
import os
import sys
import logging
from typing import List
from fastapi import APIRouter, HTTPException, Depends, status

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from fastapi_backend.models.schemas import (
    ConversationInfo, ConversationListResponse, CreateConversationResponse, SuccessResponse
)
from fastapi_backend.dependencies import get_current_user
from db_operator import (
    get_user_conversations_list, create_new_conversation, 
    delete_conversation, get_conversation_data, set_current_conversation
)

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/", response_model=ConversationListResponse)
async def get_conversations(current_user: dict = Depends(get_current_user)):
    """获取用户的对话列表"""
    try:
        conversations_data = get_user_conversations_list(current_user["id"])
        conversations = [ConversationInfo(**conv) for conv in conversations_data]
        
        return ConversationListResponse(conversations=conversations)
    
    except Exception as e:
        logger.error(f"获取对话列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取对话列表失败"
        )


@router.post("/", response_model=CreateConversationResponse)
async def create_conversation(current_user: dict = Depends(get_current_user)):
    """创建新对话"""
    try:
        conversation_data = create_new_conversation(current_user["id"])
        if not conversation_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="创建对话失败"
            )
        
        conversation = ConversationInfo(**conversation_data)
        return CreateConversationResponse(conversation=conversation)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建对话失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建对话失败"
        )


@router.get("/{conversation_id}", response_model=ConversationInfo)
async def get_conversation(
    conversation_id: int,
    current_user: dict = Depends(get_current_user)
):
    """获取指定对话信息"""
    try:
        conversation_data = get_conversation_data(conversation_id)
        if not conversation_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="对话不存在"
            )
        
        # 检查对话是否属于当前用户
        if conversation_data["user_id"] != current_user["id"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此对话"
            )
        
        return ConversationInfo(**conversation_data)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取对话信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取对话信息失败"
        )


@router.delete("/{conversation_id}", response_model=SuccessResponse)
async def delete_conversation_endpoint(
    conversation_id: int,
    current_user: dict = Depends(get_current_user)
):
    """删除指定对话"""
    try:
        # 先检查对话是否存在且属于当前用户
        conversation_data = get_conversation_data(conversation_id)
        if not conversation_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="对话不存在"
            )
        
        if conversation_data["user_id"] != current_user["id"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权删除此对话"
            )
        
        # 删除对话
        success = delete_conversation(conversation_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="删除对话失败"
            )
        
        return SuccessResponse(message="对话删除成功")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除对话失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除对话失败"
        )


@router.post("/{conversation_id}/select", response_model=SuccessResponse)
async def select_conversation(
    conversation_id: int,
    current_user: dict = Depends(get_current_user)
):
    """选择当前对话"""
    try:
        # 检查对话是否存在且属于当前用户
        conversation_data = get_conversation_data(conversation_id)
        if not conversation_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="对话不存在"
            )
        
        if conversation_data["user_id"] != current_user["id"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此对话"
            )
        
        # 设置当前对话
        success = set_current_conversation(current_user["id"], conversation_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="设置当前对话失败"
            )
        
        return SuccessResponse(message="对话选择成功")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"选择对话失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="选择对话失败"
        )
