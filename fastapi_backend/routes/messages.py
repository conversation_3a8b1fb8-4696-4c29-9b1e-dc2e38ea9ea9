# fastapi_backend/routes/messages.py - 消息管理路由
import os
import sys
import logging
from typing import List
from fastapi import APIRouter, HTTPException, Depends, status

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from fastapi_backend.models.schemas import (
    MessageInfo, MessageListResponse, SendMessageRequest, 
    UpdateMessageRequest, SuccessResponse
)
from fastapi_backend.dependencies import get_current_user
from db_operator import (
    get_conversation_messages_list, get_message_data, 
    update_message_content, delete_message, get_conversation_data,
    create_message, get_user_model_id, get_user_temperature, get_model_data
)
from openaiSDK import OpenAISDK

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/conversation/{conversation_id}", response_model=MessageListResponse)
async def get_conversation_messages(
    conversation_id: int,
    current_user: dict = Depends(get_current_user)
):
    """获取指定对话的消息列表"""
    try:
        # 检查对话是否存在且属于当前用户
        conversation_data = get_conversation_data(conversation_id)
        if not conversation_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="对话不存在"
            )
        
        if conversation_data["user_id"] != current_user["id"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此对话"
            )
        
        # 获取消息列表
        messages_data = get_conversation_messages_list(conversation_id)
        messages = [MessageInfo(**msg) for msg in messages_data]
        
        return MessageListResponse(messages=messages)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取对话消息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取对话消息失败"
        )


@router.post("/send", response_model=MessageInfo)
async def send_message(
    request: SendMessageRequest,
    current_user: dict = Depends(get_current_user)
):
    """发送消息"""
    try:
        # 检查对话是否存在且属于当前用户
        conversation_data = get_conversation_data(request.conversation_id)
        if not conversation_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="对话不存在"
            )
        
        if conversation_data["user_id"] != current_user["id"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此对话"
            )
        
        # 获取用户设置
        model_id = request.model_id or get_user_model_id(current_user["id"])
        temperature = request.temperature or float(get_user_temperature(current_user["id"]))
        
        # 创建用户消息
        user_message = create_message(
            conversation_id=request.conversation_id,
            role="user",
            content=request.content,
            model_id=model_id,
            temperature=temperature,
            max_tokens=request.max_tokens
        )
        
        if not user_message:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="创建用户消息失败"
            )
        
        # 获取模型信息
        model_data = get_model_data(model_id)
        if not model_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="模型不存在"
            )
        
        # 调用AI模型生成回复
        try:
            sdk = OpenAISDK()
            
            # 准备消息历史
            messages = []
            conversation_messages = get_conversation_messages_list(request.conversation_id)
            for msg in conversation_messages:
                if not msg.get("is_error", False):
                    messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })
            
            # 生成AI回复
            response = sdk.generate_response(
                messages=messages,
                model_data=model_data,
                temperature=temperature,
                max_tokens=request.max_tokens
            )
            
            if response and response.get("content"):
                # 创建AI回复消息
                ai_message = create_message(
                    conversation_id=request.conversation_id,
                    role="assistant",
                    content=response["content"],
                    model_id=model_id,
                    temperature=temperature,
                    max_tokens=request.max_tokens,
                    prompt_tokens=response.get("prompt_tokens"),
                    completion_tokens=response.get("completion_tokens")
                )
                
                if ai_message:
                    return MessageInfo(**ai_message)
            
            # 如果AI回复失败，返回用户消息
            return MessageInfo(**user_message)
            
        except Exception as ai_error:
            logger.error(f"AI生成回复失败: {ai_error}")
            # 创建错误消息
            error_message = create_message(
                conversation_id=request.conversation_id,
                role="assistant",
                content="抱歉，生成回复时发生错误。",
                model_id=model_id,
                temperature=temperature,
                is_error=True,
                error_info=str(ai_error)
            )
            
            if error_message:
                return MessageInfo(**error_message)
            else:
                return MessageInfo(**user_message)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"发送消息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="发送消息失败"
        )


@router.get("/{message_id}", response_model=MessageInfo)
async def get_message(
    message_id: int,
    current_user: dict = Depends(get_current_user)
):
    """获取指定消息"""
    try:
        message_data = get_message_data(message_id)
        if not message_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="消息不存在"
            )
        
        # 检查消息所属对话是否属于当前用户
        conversation_data = get_conversation_data(message_data["conversation_id"])
        if not conversation_data or conversation_data["user_id"] != current_user["id"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此消息"
            )
        
        return MessageInfo(**message_data)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取消息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取消息失败"
        )


@router.put("/{message_id}", response_model=SuccessResponse)
async def update_message(
    message_id: int,
    request: UpdateMessageRequest,
    current_user: dict = Depends(get_current_user)
):
    """更新消息内容"""
    try:
        # 检查消息是否存在且属于当前用户
        message_data = get_message_data(message_id)
        if not message_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="消息不存在"
            )
        
        conversation_data = get_conversation_data(message_data["conversation_id"])
        if not conversation_data or conversation_data["user_id"] != current_user["id"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改此消息"
            )
        
        # 更新消息内容
        success = update_message_content(message_id, request.content)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="更新消息失败"
            )
        
        return SuccessResponse(message="消息更新成功")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新消息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新消息失败"
        )


@router.delete("/{message_id}", response_model=SuccessResponse)
async def delete_message_endpoint(
    message_id: int,
    current_user: dict = Depends(get_current_user)
):
    """删除消息"""
    try:
        # 检查消息是否存在且属于当前用户
        message_data = get_message_data(message_id)
        if not message_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="消息不存在"
            )
        
        conversation_data = get_conversation_data(message_data["conversation_id"])
        if not conversation_data or conversation_data["user_id"] != current_user["id"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权删除此消息"
            )
        
        # 删除消息
        success = delete_message(message_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="删除消息失败"
            )
        
        return SuccessResponse(message="消息删除成功")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除消息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除消息失败"
        )
