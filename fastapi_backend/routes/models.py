# fastapi_backend/routes/models.py - 模型管理路由
import os
import sys
import logging
from fastapi import APIRouter, HTTPException, Depends, status

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from fastapi_backend.models.schemas import (
    ModelInfo, ModelListResponse, UserModelSettings, SuccessResponse
)
from fastapi_backend.dependencies import get_current_user
from fastapi_backend.services.external_api import external_api_service
from db_operator import (
    get_all_models, get_model_data, get_user_model_id,
    get_user_temperature, update_user_model_settings
)

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/", response_model=ModelListResponse)
async def get_models(current_user: dict = Depends(get_current_user)):
    """获取所有可用模型列表"""
    try:
        # 优先从外部模型服务获取
        models_data = await external_api_service.get_models_from_service()

        # 如果外部服务不可用，回退到本地数据库
        if not models_data:
            logger.warning("模型服务不可用，使用本地数据库")
            models_data = get_all_models()

        models = [ModelInfo(**model) for model in models_data]

        return ModelListResponse(models=models)

    except Exception as e:
        logger.error(f"获取模型列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取模型列表失败"
        )


@router.get("/{model_id}", response_model=ModelInfo)
async def get_model(
    model_id: int,
    current_user: dict = Depends(get_current_user)
):
    """获取指定模型信息"""
    try:
        # 优先从外部模型服务获取
        model_data = await external_api_service.get_model_info_from_service(model_id)

        # 如果外部服务不可用，回退到本地数据库
        if not model_data:
            logger.warning("模型服务不可用，使用本地数据库")
            model_data = get_model_data(model_id)

        if not model_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="模型不存在"
            )

        return ModelInfo(**model_data)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取模型信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取模型信息失败"
        )


@router.get("/user/current", response_model=UserModelSettings)
async def get_user_model_settings(current_user: dict = Depends(get_current_user)):
    """获取用户当前模型设置"""
    try:
        user_id = current_user["id"]
        current_model_id = get_user_model_id(user_id)
        current_temperature = float(get_user_temperature(user_id))
        
        return UserModelSettings(
            current_model_id=current_model_id,
            current_temperature=current_temperature
        )
    
    except Exception as e:
        logger.error(f"获取用户模型设置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户模型设置失败"
        )


@router.put("/user/current", response_model=SuccessResponse)
async def update_user_model_settings_endpoint(
    settings: UserModelSettings,
    current_user: dict = Depends(get_current_user)
):
    """更新用户模型设置"""
    try:
        user_id = current_user["id"]
        
        # 验证模型是否存在
        model_data = get_model_data(settings.current_model_id)
        if not model_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="指定的模型不存在"
            )
        
        # 验证温度值范围
        if not (0.0 <= settings.current_temperature <= 2.0):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="温度值必须在0.0到2.0之间"
            )
        
        # 更新设置
        success = update_user_model_settings(
            user_id, 
            settings.current_model_id, 
            settings.current_temperature
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="更新模型设置失败"
            )
        
        return SuccessResponse(message="模型设置更新成功")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新用户模型设置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新用户模型设置失败"
        )
