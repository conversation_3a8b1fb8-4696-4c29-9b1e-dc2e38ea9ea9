# fastapi_backend/routes/users.py - 用户管理路由
import os
import sys
import logging
from fastapi import APIRouter, HTTPException, Depends, status

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from fastapi_backend.models.schemas import (
    UserInfo, UserStats, UpdateSettingsRequest, SuccessResponse
)
from fastapi_backend.dependencies import get_current_user
from db_operator import (
    get_user_data, get_user_statistics, 
    update_user_mathjax_preference, update_user_model_settings
)

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/me", response_model=UserInfo)
async def get_current_user_info(current_user: dict = Depends(get_current_user)):
    """获取当前用户信息"""
    try:
        user_data = get_user_data(current_user["id"])
        if not user_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        return UserInfo(**user_data)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户信息失败"
        )


@router.get("/me/stats", response_model=UserStats)
async def get_current_user_stats(current_user: dict = Depends(get_current_user)):
    """获取当前用户统计信息"""
    try:
        stats = get_user_statistics(current_user["id"])
        if not stats:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="无法获取用户统计信息"
            )
        
        return UserStats(**stats)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户统计信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户统计信息失败"
        )


@router.put("/me/settings", response_model=SuccessResponse)
async def update_user_settings(
    request: UpdateSettingsRequest,
    current_user: dict = Depends(get_current_user)
):
    """更新用户设置"""
    try:
        user_id = current_user["id"]
        
        # 更新MathJax设置
        if request.mathjax is not None:
            success = update_user_mathjax_preference(user_id, request.mathjax)
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="更新MathJax设置失败"
                )
        
        # 更新模型设置
        if request.current_model_id is not None or request.current_temperature is not None:
            success = update_user_model_settings(
                user_id, 
                request.current_model_id, 
                request.current_temperature
            )
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="更新模型设置失败"
                )
        
        return SuccessResponse(message="设置更新成功")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新用户设置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新用户设置失败"
        )
