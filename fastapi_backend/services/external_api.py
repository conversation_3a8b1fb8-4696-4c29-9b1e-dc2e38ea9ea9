# fastapi_backend/services/external_api.py - 外部API服务连接

import os
import logging
import httpx
from typing import Dict, List, Optional, Any
from fastapi import HTTPException

logger = logging.getLogger(__name__)


class ExternalAPIService:
    """外部API服务连接类"""
    
    def __init__(self):
        # 从环境变量获取服务地址，默认为本地地址
        self.model_service_url = os.getenv('MODEL_SERVICE_URL', 'http://localhost:5001')
        self.chat_service_url = os.getenv('CHAT_SERVICE_URL', 'http://localhost:5002')
        
        # 设置超时时间
        self.timeout = httpx.Timeout(30.0)
    
    async def get_models_from_service(self) -> List[Dict[str, Any]]:
        """从模型服务获取模型列表"""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(f"{self.model_service_url}/api/v1/models")
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        return data.get('models', [])
                    else:
                        logger.error(f"模型服务返回错误: {data.get('error')}")
                        return []
                else:
                    logger.error(f"模型服务请求失败: {response.status_code}")
                    return []
                    
        except httpx.RequestError as e:
            logger.error(f"连接模型服务失败: {e}")
            return []
        except Exception as e:
            logger.error(f"获取模型列表时发生未知错误: {e}")
            return []
    
    async def get_model_info_from_service(self, model_id: int) -> Optional[Dict[str, Any]]:
        """从模型服务获取指定模型信息"""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(f"{self.model_service_url}/api/v1/models/{model_id}")
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        return data.get('model')
                    else:
                        logger.error(f"模型服务返回错误: {data.get('error')}")
                        return None
                elif response.status_code == 404:
                    return None
                else:
                    logger.error(f"模型服务请求失败: {response.status_code}")
                    return None
                    
        except httpx.RequestError as e:
            logger.error(f"连接模型服务失败: {e}")
            return None
        except Exception as e:
            logger.error(f"获取模型信息时发生未知错误: {e}")
            return None
    
    async def get_platforms_from_service(self) -> List[Dict[str, Any]]:
        """从模型服务获取平台列表"""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(f"{self.model_service_url}/api/v1/platforms")
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        return data.get('platforms', [])
                    else:
                        logger.error(f"模型服务返回错误: {data.get('error')}")
                        return []
                else:
                    logger.error(f"模型服务请求失败: {response.status_code}")
                    return []
                    
        except httpx.RequestError as e:
            logger.error(f"连接模型服务失败: {e}")
            return []
        except Exception as e:
            logger.error(f"获取平台列表时发生未知错误: {e}")
            return []
    
    async def proxy_chat_request(self, api_key: str, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """代理聊天请求到聊天服务"""
        try:
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.chat_service_url}/api/v1/chat/completions",
                    json=request_data,
                    headers=headers
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    error_data = response.json() if response.headers.get('content-type', '').startswith('application/json') else {}
                    raise HTTPException(
                        status_code=response.status_code,
                        detail=error_data.get('error', f'聊天服务请求失败: {response.status_code}')
                    )
                    
        except httpx.RequestError as e:
            logger.error(f"连接聊天服务失败: {e}")
            raise HTTPException(
                status_code=503,
                detail="聊天服务不可用"
            )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"代理聊天请求时发生未知错误: {e}")
            raise HTTPException(
                status_code=500,
                detail="代理聊天请求失败"
            )
    
    async def proxy_chat_stream_request(self, api_key: str, request_data: Dict[str, Any]):
        """代理流式聊天请求到聊天服务"""
        try:
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }
            
            # 确保请求是流式的
            request_data['stream'] = True
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                async with client.stream(
                    'POST',
                    f"{self.chat_service_url}/api/v1/chat/completions",
                    json=request_data,
                    headers=headers
                ) as response:
                    if response.status_code != 200:
                        error_data = await response.aread()
                        try:
                            error_json = response.json()
                            error_msg = error_json.get('error', f'聊天服务请求失败: {response.status_code}')
                        except:
                            error_msg = f'聊天服务请求失败: {response.status_code}'
                        
                        raise HTTPException(
                            status_code=response.status_code,
                            detail=error_msg
                        )
                    
                    async for chunk in response.aiter_bytes():
                        yield chunk
                        
        except httpx.RequestError as e:
            logger.error(f"连接聊天服务失败: {e}")
            raise HTTPException(
                status_code=503,
                detail="聊天服务不可用"
            )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"代理流式聊天请求时发生未知错误: {e}")
            raise HTTPException(
                status_code=500,
                detail="代理流式聊天请求失败"
            )
    
    async def check_services_health(self) -> Dict[str, bool]:
        """检查外部服务健康状态"""
        health_status = {
            'model_service': False,
            'chat_service': False
        }
        
        # 检查模型服务
        try:
            async with httpx.AsyncClient(timeout=httpx.Timeout(5.0)) as client:
                response = await client.get(f"{self.model_service_url}/health")
                health_status['model_service'] = response.status_code == 200
        except:
            pass
        
        # 检查聊天服务
        try:
            async with httpx.AsyncClient(timeout=httpx.Timeout(5.0)) as client:
                response = await client.get(f"{self.chat_service_url}/health")
                health_status['chat_service'] = response.status_code == 200
        except:
            pass
        
        return health_status


# 全局实例
external_api_service = ExternalAPIService()
