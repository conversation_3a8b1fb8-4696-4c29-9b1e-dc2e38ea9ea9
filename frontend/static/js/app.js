// frontend/static/js/app.js - 主应用脚本

class InspirFlowApp {
    constructor() {
        this.apiBaseUrl = '/api/v1';
        this.accessToken = localStorage.getItem('access_token');
        this.currentUser = null;
        this.currentConversation = null;
        this.conversations = [];
        this.models = [];
        this.uploadedImages = [];
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setupMathJax();
        
        // 检查是否已登录
        if (this.accessToken) {
            this.showMainApp();
            this.loadUserData();
        } else {
            this.showLogin();
        }
    }
    
    setupEventListeners() {
        // 登录相关
        document.getElementById('login-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });
        
        document.getElementById('logout-btn').addEventListener('click', () => {
            this.handleLogout();
        });
        
        // 对话相关
        document.getElementById('new-conversation-btn').addEventListener('click', () => {
            this.createNewConversation();
        });
        
        // 消息相关
        document.getElementById('send-btn').addEventListener('click', () => {
            this.sendMessage();
        });
        
        document.getElementById('message-input').addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        // 图片上传
        document.getElementById('image-upload-btn').addEventListener('click', () => {
            document.getElementById('image-input').click();
        });
        
        document.getElementById('image-input').addEventListener('change', (e) => {
            this.handleImageUpload(e);
        });
        
        // 模型和温度设置
        document.getElementById('model-select').addEventListener('change', (e) => {
            this.updateModelSettings();
        });
        
        document.getElementById('temperature-range').addEventListener('input', (e) => {
            document.getElementById('temperature-value').textContent = e.target.value;
            this.updateModelSettings();
        });
        
        // 设置相关
        document.getElementById('settings-btn').addEventListener('click', () => {
            this.showSettings();
        });
        
        document.getElementById('save-settings-btn').addEventListener('click', () => {
            this.saveSettings();
        });
        
        // 管理员面板
        document.getElementById('admin-btn').addEventListener('click', () => {
            this.showAdminPanel();
        });
        
        document.getElementById('create-user-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.createUser();
        });
    }
    
    setupMathJax() {
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    }
    
    // API请求方法
    async apiRequest(endpoint, options = {}) {
        const url = `${this.apiBaseUrl}${endpoint}`;
        const headers = {
            'Content-Type': 'application/json',
            ...options.headers
        };
        
        if (this.accessToken) {
            headers['Authorization'] = `Bearer ${this.accessToken}`;
        }
        
        try {
            const response = await fetch(url, {
                ...options,
                headers
            });
            
            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.detail || error.error || '请求失败');
            }
            
            return await response.json();
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }
    
    // 认证相关方法
    async handleLogin() {
        const apiKey = document.getElementById('api-key-input').value.trim();
        const loginBtn = document.getElementById('login-btn');
        const statusDiv = document.getElementById('login-status');
        
        if (!apiKey) {
            this.showStatus(statusDiv, 'error', '请输入API密钥');
            return;
        }
        
        loginBtn.disabled = true;
        loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>登录中...';
        
        try {
            const response = await this.apiRequest('/auth/login', {
                method: 'POST',
                body: JSON.stringify({ api_key: apiKey })
            });
            
            this.accessToken = response.access_token;
            localStorage.setItem('access_token', this.accessToken);
            
            this.currentUser = {
                id: response.user_id,
                permission: response.permission,
                is_admin: response.is_admin
            };
            
            this.showStatus(statusDiv, 'success', '登录成功');
            setTimeout(() => {
                this.showMainApp();
                this.loadUserData();
            }, 1000);
            
        } catch (error) {
            this.showStatus(statusDiv, 'error', error.message);
        } finally {
            loginBtn.disabled = false;
            loginBtn.innerHTML = '<i class="fas fa-sign-in-alt me-2"></i>登录';
        }
    }
    
    handleLogout() {
        this.accessToken = null;
        this.currentUser = null;
        this.currentConversation = null;
        this.conversations = [];
        localStorage.removeItem('access_token');
        this.showLogin();
    }
    
    // UI显示方法
    showLogin() {
        document.getElementById('login-container').style.display = 'flex';
        document.getElementById('main-app').style.display = 'none';
        document.getElementById('user-menu').style.display = 'none';
    }
    
    showMainApp() {
        document.getElementById('login-container').style.display = 'none';
        document.getElementById('main-app').style.display = 'flex';
        document.getElementById('user-menu').style.display = 'block';
        
        // 显示管理员按钮
        if (this.currentUser && this.currentUser.is_admin) {
            document.getElementById('admin-btn').style.display = 'block';
        }
    }
    
    showStatus(container, type, message) {
        const alertClass = type === 'error' ? 'alert-danger' : 
                          type === 'success' ? 'alert-success' : 'alert-warning';
        
        container.innerHTML = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
    }
    
    // 数据加载方法
    async loadUserData() {
        try {
            // 加载用户信息
            const userInfo = await this.apiRequest('/users/me');
            document.getElementById('user-info').textContent = `用户 ${userInfo.id}`;
            
            // 加载对话列表
            await this.loadConversations();
            
            // 加载模型列表
            await this.loadModels();
            
        } catch (error) {
            console.error('加载用户数据失败:', error);
            this.handleLogout();
        }
    }
    
    async loadConversations() {
        try {
            const response = await this.apiRequest('/conversations/');
            this.conversations = response.conversations;
            this.renderConversations();
        } catch (error) {
            console.error('加载对话列表失败:', error);
        }
    }
    
    async loadModels() {
        try {
            const response = await this.apiRequest('/models/');
            this.models = response.models;
            this.renderModels();
            
            // 加载用户当前模型设置
            const settings = await this.apiRequest('/models/user/current');
            document.getElementById('model-select').value = settings.current_model_id;
            document.getElementById('temperature-range').value = settings.current_temperature;
            document.getElementById('temperature-value').textContent = settings.current_temperature;
            
        } catch (error) {
            console.error('加载模型列表失败:', error);
        }
    }
    
    // 渲染方法
    renderConversations() {
        const container = document.getElementById('conversations-list');
        
        if (this.conversations.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">暂无对话</p>';
            return;
        }
        
        container.innerHTML = this.conversations.map(conv => `
            <div class="conversation-item ${this.currentConversation && this.currentConversation.id === conv.id ? 'active' : ''}" 
                 data-conversation-id="${conv.id}">
                <div class="conversation-title">${conv.title || '新对话'}</div>
                <div class="conversation-time">${this.formatTime(conv.latest_revised_at)}</div>
            </div>
        `).join('');
        
        // 添加点击事件
        container.querySelectorAll('.conversation-item').forEach(item => {
            item.addEventListener('click', () => {
                const conversationId = parseInt(item.dataset.conversationId);
                this.selectConversation(conversationId);
            });
        });
    }
    
    renderModels() {
        const select = document.getElementById('model-select');
        select.innerHTML = this.models.map(model => `
            <option value="${model.id}">${model.display_name}</option>
        `).join('');
    }
    
    formatTime(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) return '刚刚';
        if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
        if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
        return date.toLocaleDateString();
    }
    
    // 对话相关方法
    async createNewConversation() {
        try {
            const response = await this.apiRequest('/conversations/', {
                method: 'POST'
            });

            this.currentConversation = response.conversation;
            await this.loadConversations();
            this.clearMessages();

        } catch (error) {
            console.error('创建对话失败:', error);
        }
    }

    async selectConversation(conversationId) {
        try {
            const conversation = this.conversations.find(c => c.id === conversationId);
            if (!conversation) return;

            this.currentConversation = conversation;
            this.renderConversations();
            await this.loadMessages(conversationId);

        } catch (error) {
            console.error('选择对话失败:', error);
        }
    }

    async loadMessages(conversationId) {
        try {
            const response = await this.apiRequest(`/messages/conversation/${conversationId}`);
            this.renderMessages(response.messages);
        } catch (error) {
            console.error('加载消息失败:', error);
        }
    }

    async sendMessage() {
        const input = document.getElementById('message-input');
        const content = input.value.trim();

        if (!content && this.uploadedImages.length === 0) return;
        if (!this.currentConversation) {
            await this.createNewConversation();
        }

        const sendBtn = document.getElementById('send-btn');
        sendBtn.disabled = true;
        sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

        try {
            const response = await this.apiRequest('/messages/send', {
                method: 'POST',
                body: JSON.stringify({
                    conversation_id: this.currentConversation.id,
                    content: content,
                    images: this.uploadedImages
                })
            });

            input.value = '';
            this.uploadedImages = [];
            this.updateImagePreview();

            await this.loadMessages(this.currentConversation.id);
            await this.loadConversations();

        } catch (error) {
            console.error('发送消息失败:', error);
            this.showStatus(document.getElementById('send-status'), 'error', error.message);
        } finally {
            sendBtn.disabled = false;
            sendBtn.innerHTML = '<i class="fas fa-paper-plane"></i>';
        }
    }

    renderMessages(messages) {
        const container = document.getElementById('messages-container');

        if (messages.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted mt-5">
                    <i class="fas fa-comments fa-3x mb-3"></i>
                    <p>开始新的对话吧！</p>
                </div>
            `;
            return;
        }

        container.innerHTML = messages.map(message => this.renderMessage(message)).join('');
        container.scrollTop = container.scrollHeight;

        // 渲染MathJax
        if (window.MathJax) {
            MathJax.typesetPromise([container]);
        }
    }

    renderMessage(message) {
        const isUser = message.role === 'user';
        const avatar = isUser ? 'fa-user' : 'fa-robot';
        const messageClass = isUser ? 'user' : 'assistant';

        return `
            <div class="message ${messageClass} fade-in">
                <div class="message-avatar">
                    <i class="fas ${avatar}"></i>
                </div>
                <div class="message-content">
                    <div class="message-text">${this.formatMessageContent(message.content)}</div>
                    <div class="message-time">${this.formatTime(message.created_at)}</div>
                    ${!isUser ? this.renderMessageActions(message) : ''}
                </div>
            </div>
        `;
    }

    renderMessageActions(message) {
        return `
            <div class="message-actions">
                <button class="btn btn-sm btn-outline-secondary" onclick="app.editMessage(${message.id})">
                    <i class="fas fa-edit"></i> 编辑
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="app.deleteMessage(${message.id})">
                    <i class="fas fa-trash"></i> 删除
                </button>
            </div>
        `;
    }

    clearMessages() {
        document.getElementById('messages-container').innerHTML = `
            <div class="text-center text-muted mt-5">
                <i class="fas fa-comments fa-3x mb-3"></i>
                <p>开始新的对话吧！</p>
            </div>
        `;
    }

    // 图片处理方法
    async handleImageUpload(event) {
        const files = Array.from(event.target.files);

        for (const file of files) {
            if (file.type.startsWith('image/')) {
                try {
                    const base64 = await this.fileToBase64(file);
                    this.uploadedImages.push(base64);
                } catch (error) {
                    console.error('图片处理失败:', error);
                }
            }
        }

        this.updateImagePreview();
        event.target.value = '';
    }

    fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }

    updateImagePreview() {
        const preview = document.getElementById('image-preview');

        if (this.uploadedImages.length === 0) {
            preview.style.display = 'none';
            return;
        }

        preview.style.display = 'block';
        preview.innerHTML = this.uploadedImages.map((img, index) => `
            <div class="image-preview-item">
                <img src="${img}" class="image-preview-img" alt="预览图片">
                <button class="image-remove-btn" onclick="app.removeImage(${index})">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `).join('');
    }

    removeImage(index) {
        this.uploadedImages.splice(index, 1);
        this.updateImagePreview();
    }

    // 工具方法
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatMessageContent(content) {
        // 简单的Markdown渲染
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
            .replace(/\n/g, '<br>');
    }

    // 设置相关方法
    async showSettings() {
        try {
            // 加载用户信息和统计
            const userInfo = await this.apiRequest('/users/me');
            const userStats = await this.apiRequest('/users/me/stats');

            // 设置MathJax开关
            document.getElementById('mathjax-switch').checked = userInfo.mathjax;

            // 显示用户统计
            document.getElementById('user-stats').innerHTML = `
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h5 mb-0">${userStats.total_conversations}</div>
                            <small class="text-muted">对话数</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h5 mb-0">${userStats.total_messages}</div>
                            <small class="text-muted">消息数</small>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h5 mb-0">${userStats.total_tokens}</div>
                            <small class="text-muted">总Token数</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h5 mb-0 text-success">¥${userStats.current_balance.toFixed(2)}</div>
                            <small class="text-muted">余额</small>
                        </div>
                    </div>
                </div>
            `;

            // 显示设置模态框
            const modal = new bootstrap.Modal(document.getElementById('settings-modal'));
            modal.show();

        } catch (error) {
            console.error('加载设置失败:', error);
        }
    }

    async saveSettings() {
        try {
            const mathjax = document.getElementById('mathjax-switch').checked;

            await this.apiRequest('/users/me/settings', {
                method: 'PUT',
                body: JSON.stringify({ mathjax })
            });

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('settings-modal'));
            modal.hide();

            // 如果当前有对话，重新加载消息以应用MathJax设置
            if (this.currentConversation) {
                await this.loadMessages(this.currentConversation.id);
            }

        } catch (error) {
            console.error('保存设置失败:', error);
        }
    }

    async updateModelSettings() {
        try {
            const modelId = parseInt(document.getElementById('model-select').value);
            const temperature = parseFloat(document.getElementById('temperature-range').value);

            await this.apiRequest('/models/user/current', {
                method: 'PUT',
                body: JSON.stringify({
                    current_model_id: modelId,
                    current_temperature: temperature
                })
            });

        } catch (error) {
            console.error('更新模型设置失败:', error);
        }
    }

    // 管理员功能
    async showAdminPanel() {
        try {
            await this.loadUsers();

            const modal = new bootstrap.Modal(document.getElementById('admin-modal'));
            modal.show();

        } catch (error) {
            console.error('加载管理员面板失败:', error);
        }
    }

    async loadUsers() {
        try {
            const response = await this.apiRequest('/admin/users');
            this.renderUsersTable(response.users);
        } catch (error) {
            console.error('加载用户列表失败:', error);
        }
    }

    renderUsersTable(users) {
        const container = document.getElementById('users-table-container');

        container.innerHTML = `
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>API密钥</th>
                            <th>权限</th>
                            <th>状态</th>
                            <th>余额</th>
                            <th>对话数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${users.map(user => `
                            <tr>
                                <td>${user.id}</td>
                                <td><code>${user.api_key.substring(0, 20)}...</code></td>
                                <td>
                                    <span class="badge ${user.permission === 9 ? 'bg-danger' : 'bg-primary'}">
                                        ${user.permission === 9 ? '管理员' : '普通用户'}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge ${user.is_active ? 'bg-success' : 'bg-secondary'}">
                                        ${user.is_active ? '激活' : '禁用'}
                                    </span>
                                </td>
                                <td>¥${user.current_balance}</td>
                                <td>${user.total_conversations}</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary me-1"
                                            onclick="app.toggleUserStatus(${user.id})">
                                        ${user.is_active ? '禁用' : '激活'}
                                    </button>
                                    <button class="btn btn-sm btn-outline-success"
                                            onclick="app.addUserBalance(${user.id})">
                                        充值
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    async createUser() {
        try {
            const permission = parseInt(document.getElementById('user-permission').value);
            const initialBalance = parseFloat(document.getElementById('initial-balance').value);

            const response = await this.apiRequest('/admin/users', {
                method: 'POST',
                body: JSON.stringify({
                    permission,
                    initial_balance: initialBalance
                })
            });

            document.getElementById('create-user-result').innerHTML = `
                <div class="alert alert-success">
                    <h6>用户创建成功！</h6>
                    <p><strong>API密钥:</strong> <code>${response.api_key}</code></p>
                    <p class="mb-0"><small class="text-muted">请保存此API密钥，它不会再次显示。</small></p>
                </div>
            `;

            // 重新加载用户列表
            await this.loadUsers();

        } catch (error) {
            document.getElementById('create-user-result').innerHTML = `
                <div class="alert alert-danger">
                    创建用户失败: ${error.message}
                </div>
            `;
        }
    }

    async toggleUserStatus(userId) {
        try {
            await this.apiRequest('/admin/users/toggle-status', {
                method: 'POST',
                body: JSON.stringify({ user_id: userId })
            });

            await this.loadUsers();

        } catch (error) {
            console.error('切换用户状态失败:', error);
        }
    }

    async addUserBalance(userId) {
        const amount = prompt('请输入充值金额:');
        if (!amount || isNaN(amount)) return;

        try {
            await this.apiRequest('/admin/users/balance', {
                method: 'POST',
                body: JSON.stringify({
                    user_id: userId,
                    amount: parseFloat(amount),
                    description: '管理员充值'
                })
            });

            await this.loadUsers();

        } catch (error) {
            console.error('充值失败:', error);
        }
    }

    // 消息操作
    async editMessage(messageId) {
        // 这里可以实现消息编辑功能
        console.log('编辑消息:', messageId);
    }

    async deleteMessage(messageId) {
        if (!confirm('确定要删除这条消息吗？')) return;

        try {
            await this.apiRequest(`/messages/${messageId}`, {
                method: 'DELETE'
            });

            if (this.currentConversation) {
                await this.loadMessages(this.currentConversation.id);
            }

        } catch (error) {
            console.error('删除消息失败:', error);
        }
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new InspirFlowApp();
});
