#!/usr/bin/env python3
# manage_api_db.py - API服务数据库管理脚本
import sys
import logging
import argparse
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from api_db_operations import APIDBOperations
from db_models import init_db

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def init_database():
    """初始化数据库"""
    logger.info("初始化数据库...")
    try:
        init_db()
        logger.info("数据库初始化完成")
        return True
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        return False


def create_application(name: str, description: str = None):
    """创建新应用"""
    logger.info(f"创建应用: {name}")
    
    app_info = APIDBOperations.create_application(name, description)
    if app_info:
        logger.info(f"应用创建成功:")
        logger.info(f"  ID: {app_info['id']}")
        logger.info(f"  名称: {app_info['name']}")
        logger.info(f"  API密钥: {app_info['api_key']}")
        logger.info(f"  创建时间: {app_info['created_at']}")
        return app_info
    else:
        logger.error("应用创建失败")
        return None


def list_applications():
    """列出所有应用"""
    logger.info("获取应用列表...")
    
    applications = APIDBOperations.list_all_applications()
    if applications:
        logger.info(f"找到 {len(applications)} 个应用:")
        for app in applications:
            logger.info(f"  ID: {app['id']}, 名称: {app['name']}, "
                       f"模型数量: {app['model_count']}, API密钥: {app['api_key']}")
    else:
        logger.info("没有找到应用")
    
    return applications


def add_model_to_app(app_id: int, model_id: int, is_default: bool = False):
    """为应用添加模型授权"""
    logger.info(f"为应用 {app_id} 添加模型 {model_id} 授权...")
    
    success = APIDBOperations.add_model_to_application(app_id, model_id, is_default)
    if success:
        logger.info("模型授权添加成功")
        if is_default:
            logger.info("已设置为默认模型")
    else:
        logger.error("模型授权添加失败")
    
    return success


def show_app_models(app_id: int):
    """显示应用的模型列表"""
    logger.info(f"获取应用 {app_id} 的模型列表...")
    
    models = APIDBOperations.get_application_models(app_id)
    if models:
        logger.info(f"应用 {app_id} 有 {len(models)} 个授权模型:")
        for model in models:
            default_mark = " (默认)" if model['is_default'] else ""
            logger.info(f"  ID: {model['id']}, 名称: {model['name']}, "
                       f"平台: {model['platform']}{default_mark}")
    else:
        logger.info(f"应用 {app_id} 没有授权模型")
    
    return models


def test_api_key(api_key: str):
    """测试API密钥"""
    logger.info(f"测试API密钥: {api_key}")
    
    app = APIDBOperations.verify_api_key(api_key)
    if app:
        logger.info("API密钥验证成功:")
        logger.info(f"  应用ID: {app.id}")
        logger.info(f"  应用名称: {app.name}")
        logger.info(f"  描述: {app.description}")
        
        # 显示授权模型
        models = APIDBOperations.get_application_models(app.id)
        logger.info(f"  授权模型数量: {len(models)}")
        for model in models:
            default_mark = " (默认)" if model['is_default'] else ""
            logger.info(f"    - {model['name']} ({model['platform']}){default_mark}")
        
        return True
    else:
        logger.error("API密钥验证失败")
        return False


def main():
    parser = argparse.ArgumentParser(description='InspirFlow API服务数据库管理')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 初始化数据库
    subparsers.add_parser('init', help='初始化数据库')
    
    # 创建应用
    create_parser = subparsers.add_parser('create-app', help='创建新应用')
    create_parser.add_argument('name', help='应用名称')
    create_parser.add_argument('--description', help='应用描述')
    
    # 列出应用
    subparsers.add_parser('list-apps', help='列出所有应用')
    
    # 添加模型授权
    add_model_parser = subparsers.add_parser('add-model', help='为应用添加模型授权')
    add_model_parser.add_argument('app_id', type=int, help='应用ID')
    add_model_parser.add_argument('model_id', type=int, help='模型ID')
    add_model_parser.add_argument('--default', action='store_true', help='设置为默认模型')
    
    # 显示应用模型
    show_models_parser = subparsers.add_parser('show-models', help='显示应用的模型列表')
    show_models_parser.add_argument('app_id', type=int, help='应用ID')
    
    # 测试API密钥
    test_parser = subparsers.add_parser('test-key', help='测试API密钥')
    test_parser.add_argument('api_key', help='API密钥')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        if args.command == 'init':
            init_database()
        elif args.command == 'create-app':
            create_application(args.name, args.description)
        elif args.command == 'list-apps':
            list_applications()
        elif args.command == 'add-model':
            add_model_to_app(args.app_id, args.model_id, args.default)
        elif args.command == 'show-models':
            show_app_models(args.app_id)
        elif args.command == 'test-key':
            test_api_key(args.api_key)
        else:
            logger.error(f"未知命令: {args.command}")
            
    except Exception as e:
        logger.error(f"执行命令失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
