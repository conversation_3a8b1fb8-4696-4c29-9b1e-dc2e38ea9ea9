# platform_adapters.py - 不同平台的API适配器
import logging
from typing import Dict, Any, Optional
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class PlatformAdapter(ABC):
    """平台适配器基类"""
    
    @abstractmethod
    def adapt_request(self, data: Dict[str, Any], model_info: Dict[str, Any]) -> Dict[str, Any]:
        """适配请求数据"""
        pass
    
    @abstractmethod
    def get_endpoint_url(self, base_url: str) -> str:
        """获取端点URL"""
        pass
    
    @abstractmethod
    def get_headers(self, api_key: str) -> Dict[str, str]:
        """获取请求头"""
        pass


class OpenAIAdapter(PlatformAdapter):
    """OpenAI兼容平台适配器"""
    
    def adapt_request(self, data: Dict[str, Any], model_info: Dict[str, Any]) -> Dict[str, Any]:
        """适配OpenAI格式的请求"""
        adapted_data = {
            'model': model_info['internal_name'],
            'messages': data.get('messages', []),
            'stream': data.get('stream', False)
        }
        
        # 添加可选参数
        optional_params = [
            'temperature', 'max_tokens', 'top_p', 
            'frequency_penalty', 'presence_penalty', 'stop'
        ]
        
        for param in optional_params:
            if param in data:
                adapted_data[param] = data[param]
        
        return adapted_data
    
    def get_endpoint_url(self, base_url: str) -> str:
        """获取OpenAI格式的端点URL"""
        return f"{base_url.rstrip('/')}/chat/completions"
    
    def get_headers(self, api_key: str) -> Dict[str, str]:
        """获取OpenAI格式的请求头"""
        return {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }


class AnthropicAdapter(PlatformAdapter):
    """Anthropic Claude适配器"""
    
    def adapt_request(self, data: Dict[str, Any], model_info: Dict[str, Any]) -> Dict[str, Any]:
        """适配Anthropic格式的请求"""
        messages = data.get('messages', [])
        
        # Anthropic需要分离system消息
        system_message = None
        user_messages = []
        
        for msg in messages:
            if msg.get('role') == 'system':
                system_message = msg.get('content', '')
            else:
                user_messages.append(msg)
        
        adapted_data = {
            'model': model_info['internal_name'],
            'messages': user_messages,
            'max_tokens': data.get('max_tokens', 1024),
            'stream': data.get('stream', False)
        }
        
        if system_message:
            adapted_data['system'] = system_message
        
        # 添加可选参数
        if 'temperature' in data:
            adapted_data['temperature'] = data['temperature']
        if 'top_p' in data:
            adapted_data['top_p'] = data['top_p']
        if 'stop' in data:
            adapted_data['stop_sequences'] = data['stop']
        
        return adapted_data
    
    def get_endpoint_url(self, base_url: str) -> str:
        """获取Anthropic格式的端点URL"""
        return f"{base_url.rstrip('/')}/v1/messages"
    
    def get_headers(self, api_key: str) -> Dict[str, str]:
        """获取Anthropic格式的请求头"""
        return {
            'x-api-key': api_key,
            'Content-Type': 'application/json',
            'anthropic-version': '2023-06-01'
        }


class DashScopeAdapter(PlatformAdapter):
    """阿里云DashScope适配器"""
    
    def adapt_request(self, data: Dict[str, Any], model_info: Dict[str, Any]) -> Dict[str, Any]:
        """适配DashScope格式的请求"""
        # DashScope使用OpenAI兼容格式
        return OpenAIAdapter().adapt_request(data, model_info)
    
    def get_endpoint_url(self, base_url: str) -> str:
        """获取DashScope格式的端点URL"""
        return f"{base_url.rstrip('/')}/chat/completions"
    
    def get_headers(self, api_key: str) -> Dict[str, str]:
        """获取DashScope格式的请求头"""
        return {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }


class DeepSeekAdapter(PlatformAdapter):
    """DeepSeek适配器"""
    
    def adapt_request(self, data: Dict[str, Any], model_info: Dict[str, Any]) -> Dict[str, Any]:
        """适配DeepSeek格式的请求"""
        # DeepSeek使用OpenAI兼容格式
        return OpenAIAdapter().adapt_request(data, model_info)
    
    def get_endpoint_url(self, base_url: str) -> str:
        """获取DeepSeek格式的端点URL"""
        return f"{base_url.rstrip('/')}/chat/completions"
    
    def get_headers(self, api_key: str) -> Dict[str, str]:
        """获取DeepSeek格式的请求头"""
        return {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }


class OpenRouterAdapter(PlatformAdapter):
    """OpenRouter适配器"""
    
    def adapt_request(self, data: Dict[str, Any], model_info: Dict[str, Any]) -> Dict[str, Any]:
        """适配OpenRouter格式的请求"""
        # OpenRouter使用OpenAI兼容格式
        return OpenAIAdapter().adapt_request(data, model_info)
    
    def get_endpoint_url(self, base_url: str) -> str:
        """获取OpenRouter格式的端点URL"""
        return f"{base_url.rstrip('/')}/chat/completions"
    
    def get_headers(self, api_key: str) -> Dict[str, str]:
        """获取OpenRouter格式的请求头"""
        return {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json',
            'HTTP-Referer': 'https://inspirflow.ai',  # OpenRouter需要referer
            'X-Title': 'InspirFlow'
        }


class PlatformAdapterFactory:
    """平台适配器工厂"""
    
    _adapters = {
        'openai': OpenAIAdapter,
        'anthropic': AnthropicAdapter,
        'dashscope': DashScopeAdapter,
        'deepseek': DeepSeekAdapter,
        'openrouter': OpenRouterAdapter,
        # 默认使用OpenAI兼容格式
        'default': OpenAIAdapter
    }
    
    @classmethod
    def get_adapter(cls, platform_name: str) -> PlatformAdapter:
        """根据平台名称获取适配器"""
        platform_name = platform_name.lower()
        adapter_class = cls._adapters.get(platform_name, cls._adapters['default'])
        return adapter_class()
    
    @classmethod
    def register_adapter(cls, platform_name: str, adapter_class: type):
        """注册新的平台适配器"""
        cls._adapters[platform_name.lower()] = adapter_class
    
    @classmethod
    def list_supported_platforms(cls) -> list:
        """列出支持的平台"""
        return list(cls._adapters.keys())
