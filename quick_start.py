#!/usr/bin/env python3
# quick_start.py - 快速启动脚本

import os
import sys
import subprocess
import argparse
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        logger.error("需要Python 3.8或更高版本")
        return False
    logger.info(f"Python版本: {sys.version}")
    return True


def check_dependencies():
    """检查并安装依赖"""
    logger.info("检查依赖...")
    
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True)
        logger.info("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ 依赖安装失败: {e}")
        return False


def create_directories():
    """创建必要的目录"""
    logger.info("创建目录结构...")
    
    directories = [
        "fastapi_backend",
        "fastapi_backend/models",
        "fastapi_backend/routes",
        "frontend",
        "frontend/static",
        "frontend/static/css",
        "frontend/static/js",
        "frontend/static/images"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    logger.info("✅ 目录结构创建完成")


def check_database_connection():
    """检查数据库连接"""
    logger.info("检查数据库连接...")
    
    try:
        from db_models import chat_db_engine, model_db_engine
        
        # 测试连接
        with chat_db_engine.connect() as conn:
            conn.execute("SELECT 1")
        
        with model_db_engine.connect() as conn:
            conn.execute("SELECT 1")
        
        logger.info("✅ 数据库连接正常")
        return True
        
    except Exception as e:
        logger.warning(f"⚠️ 数据库连接失败: {e}")
        logger.info("请检查数据库配置，或使用 --skip-db-check 跳过检查")
        return False


def start_fastapi_server(host='0.0.0.0', port=8000, reload=True):
    """启动FastAPI服务器"""
    logger.info(f"启动FastAPI服务器: http://{host}:{port}")
    
    try:
        import uvicorn
        uvicorn.run(
            "fastapi_backend.main:app",
            host=host,
            port=port,
            reload=reload,
            log_level="info"
        )
    except ImportError:
        logger.error("uvicorn未安装，尝试使用pip安装...")
        subprocess.run([sys.executable, "-m", "pip", "install", "uvicorn[standard]"])
        import uvicorn
        uvicorn.run(
            "fastapi_backend.main:app",
            host=host,
            port=port,
            reload=reload,
            log_level="info"
        )
    except KeyboardInterrupt:
        logger.info("服务器已停止")
    except Exception as e:
        logger.error(f"启动服务器失败: {e}")


def start_with_docker():
    """使用Docker启动"""
    logger.info("使用Docker启动服务...")
    
    if not subprocess.run(["docker", "--version"], capture_output=True).returncode == 0:
        logger.error("Docker未安装或未启动")
        return False
    
    if not subprocess.run(["docker-compose", "--version"], capture_output=True).returncode == 0:
        logger.error("docker-compose未安装")
        return False
    
    try:
        # 构建并启动服务
        subprocess.run([
            "docker-compose", "-f", "docker-compose-fastapi.yml", 
            "up", "--build", "-d"
        ], check=True)
        
        logger.info("✅ Docker服务启动成功")
        logger.info("FastAPI后端: http://localhost:8000")
        logger.info("API文档: http://localhost:8000/docs")
        logger.info("查看日志: docker-compose -f docker-compose-fastapi.yml logs -f")
        
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"Docker启动失败: {e}")
        return False


def show_usage_info(services=['all']):
    """显示使用信息"""
    logger.info("\n" + "="*60)
    logger.info("🎉 InspirFlow 服务启动成功！")
    logger.info("="*60)

    if 'all' in services or 'fastapi' in services:
        logger.info("📱 前端界面: http://localhost:8000")
        logger.info("📚 API文档: http://localhost:8000/docs")
        logger.info("🔍 健康检查: http://localhost:8000/health")

    if 'all' in services or 'model' in services:
        logger.info("🤖 模型服务: http://localhost:5001")

    if 'all' in services or 'chat' in services:
        logger.info("💬 聊天服务: http://localhost:5002")

    logger.info("="*60)
    logger.info("💡 使用提示:")
    logger.info("1. 使用现有的API密钥登录")
    logger.info("2. 如果没有API密钥，请联系管理员创建")
    logger.info("3. 支持所有原有功能：对话、消息、模型选择等")
    logger.info("4. 管理员可以访问管理面板")
    logger.info("5. 多服务架构提供更好的性能和扩展性")
    logger.info("="*60)


def main():
    parser = argparse.ArgumentParser(description='InspirFlow FastAPI后端快速启动')
    parser.add_argument('--host', default='0.0.0.0', help='绑定主机地址')
    parser.add_argument('--port', type=int, default=8000, help='绑定端口')
    parser.add_argument('--docker', action='store_true', help='使用Docker启动')
    parser.add_argument('--no-reload', action='store_true', help='禁用自动重载')
    parser.add_argument('--skip-deps', action='store_true', help='跳过依赖检查')
    parser.add_argument('--skip-db-check', action='store_true', help='跳过数据库检查')
    parser.add_argument('--cleanup', action='store_true', help='清理旧文件')
    parser.add_argument('--test', action='store_true', help='运行测试')
    parser.add_argument('--services', nargs='+',
                       choices=['fastapi', 'model', 'chat', 'all'],
                       default=['all'], help='要启动的服务')
    parser.add_argument('--use-all-services', action='store_true',
                       help='使用完整的多服务架构')

    args = parser.parse_args()

    logger.info("🚀 InspirFlow FastAPI后端快速启动")
    logger.info("="*50)

    # 清理旧文件
    if args.cleanup:
        logger.info("🧹 清理旧文件...")
        try:
            subprocess.run([sys.executable, "cleanup_legacy_files.py", "--execute"], check=True)
        except subprocess.CalledProcessError:
            logger.warning("清理脚本执行失败，继续启动...")

    # 检查Python版本
    if not check_python_version():
        sys.exit(1)

    # 创建目录结构
    create_directories()

    # 检查依赖
    if not args.skip_deps:
        if not check_dependencies():
            sys.exit(1)

    # 检查数据库连接
    if not args.skip_db_check:
        if not check_database_connection():
            response = input("数据库连接失败，是否继续启动？(y/N): ")
            if response.lower() != 'y':
                sys.exit(1)

    # 运行测试
    if args.test:
        logger.info("🧪 运行API测试...")
        api_key = input("请输入测试用的API密钥: ")
        if api_key:
            try:
                subprocess.run([
                    sys.executable, "test_fastapi_backend.py",
                    "--api-key", api_key, "--url", f"http://{args.host}:{args.port}"
                ], check=True)
            except subprocess.CalledProcessError:
                logger.warning("测试失败，但继续启动服务...")

    # 启动服务
    if args.docker:
        if start_with_docker():
            show_usage_info(args.services)
    elif args.use_all_services or 'all' in args.services:
        # 使用完整的多服务架构
        logger.info("🚀 启动完整的多服务架构...")
        try:
            subprocess.run([
                sys.executable, "start_all_services.py",
                "--host", args.host,
                "--fastapi-port", str(args.port),
                "--services"] + args.services,
                check=True
            )
        except subprocess.CalledProcessError as e:
            logger.error(f"启动多服务失败: {e}")
            sys.exit(1)
    else:
        show_usage_info(args.services)
        start_fastapi_server(
            host=args.host,
            port=args.port,
            reload=not args.no_reload
        )


if __name__ == "__main__":
    main()
