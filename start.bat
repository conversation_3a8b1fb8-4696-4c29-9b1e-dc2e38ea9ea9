@echo off
REM start.bat - Windows启动脚本

echo ========================================
echo    InspirFlow 快速启动
echo ========================================

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Python未安装或不在PATH中
    pause
    exit /b 1
)

REM 安装依赖
echo 检查并安装依赖...
pip install fastapi uvicorn gunicorn httpx flask flask-cors

REM 创建目录
if not exist "fastapi_backend" mkdir fastapi_backend
if not exist "fastapi_backend\models" mkdir fastapi_backend\models
if not exist "fastapi_backend\routes" mkdir fastapi_backend\routes
if not exist "fastapi_backend\services" mkdir fastapi_backend\services
if not exist "frontend" mkdir frontend
if not exist "frontend\static" mkdir frontend\static
if not exist "frontend\static\css" mkdir frontend\static\css
if not exist "frontend\static\js" mkdir frontend\static\js

echo ========================================
echo    启动服务
echo ========================================
echo 前端界面: http://localhost:8000
echo API文档: http://localhost:8000/docs
echo ========================================
echo 按 Ctrl+C 停止服务
echo ========================================

REM 启动FastAPI服务
python start_simple.py --mode simple

pause
