#!/usr/bin/env python3
# start_all_services.py - 启动所有服务的脚本

import os
import sys
import time
import signal
import subprocess
import argparse
import logging
from pathlib import Path
from threading import Thread

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ServiceManager:
    """服务管理器"""
    
    def __init__(self):
        self.processes = {}
        self.running = True
    
    def start_service(self, name, command, cwd=None):
        """启动服务"""
        logger.info(f"启动服务: {name}")
        try:
            process = subprocess.Popen(
                command,
                shell=True,
                cwd=cwd or os.getcwd(),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            self.processes[name] = process
            
            # 启动日志监控线程
            log_thread = Thread(target=self._monitor_logs, args=(name, process))
            log_thread.daemon = True
            log_thread.start()
            
            return True
            
        except Exception as e:
            logger.error(f"启动服务 {name} 失败: {e}")
            return False
    
    def _monitor_logs(self, name, process):
        """监控服务日志"""
        while self.running and process.poll() is None:
            try:
                line = process.stdout.readline()
                if line:
                    logger.info(f"[{name}] {line.strip()}")
            except:
                break
    
    def stop_all_services(self):
        """停止所有服务"""
        logger.info("停止所有服务...")
        self.running = False
        
        for name, process in self.processes.items():
            if process.poll() is None:
                logger.info(f"停止服务: {name}")
                try:
                    process.terminate()
                    process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    logger.warning(f"强制终止服务: {name}")
                    process.kill()
                except Exception as e:
                    logger.error(f"停止服务 {name} 失败: {e}")
    
    def wait_for_services(self):
        """等待所有服务"""
        try:
            while self.running:
                time.sleep(1)
                
                # 检查服务状态
                for name, process in list(self.processes.items()):
                    if process.poll() is not None:
                        logger.warning(f"服务 {name} 已退出，退出码: {process.returncode}")
                        if self.running:
                            logger.info(f"重启服务: {name}")
                            # 这里可以添加重启逻辑
                        
        except KeyboardInterrupt:
            logger.info("收到中断信号，停止所有服务...")
            self.stop_all_services()


def check_dependencies():
    """检查依赖"""
    required_packages = ['uvicorn', 'gunicorn', 'fastapi', 'flask']
    missing = []

    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing.append(package)

    if missing:
        logger.error(f"缺少必要的Python包: {', '.join(missing)}")
        logger.info("请运行: pip install -r requirements.txt")
        return False

    return True


def main():
    parser = argparse.ArgumentParser(description='启动InspirFlow所有服务')
    parser.add_argument('--host', default='0.0.0.0', help='绑定主机地址')
    parser.add_argument('--fastapi-port', type=int, default=8000, help='FastAPI端口')
    parser.add_argument('--model-port', type=int, default=5001, help='模型服务端口')
    parser.add_argument('--chat-port', type=int, default=5002, help='聊天服务端口')
    parser.add_argument('--dash-port', type=int, default=8050, help='Dash应用端口')
    parser.add_argument('--services', nargs='+', 
                       choices=['fastapi', 'model', 'chat', 'dash', 'all'],
                       default=['all'], help='要启动的服务')
    parser.add_argument('--mode', choices=['dev', 'prod'], default='dev',
                       help='运行模式')
    parser.add_argument('--skip-checks', action='store_true',
                       help='跳过依赖检查')
    
    args = parser.parse_args()
    
    logger.info("🚀 InspirFlow 服务管理器")
    logger.info("=" * 50)
    
    # 检查依赖
    if not args.skip_checks:
        if not check_dependencies():
            sys.exit(1)
    
    # 确定要启动的服务
    services_to_start = args.services
    if 'all' in services_to_start:
        services_to_start = ['fastapi', 'model', 'chat']
    
    # 创建服务管理器
    manager = ServiceManager()
    
    # 设置信号处理
    def signal_handler(signum, frame):
        logger.info("收到停止信号...")
        manager.stop_all_services()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 启动服务
    success_count = 0
    
    if 'model' in services_to_start:
        cmd = f"python start_model_service.py --host {args.host} --port {args.model_port} --mode {args.mode}"
        if manager.start_service('model-service', cmd):
            success_count += 1
            time.sleep(2)  # 等待服务启动
    
    if 'chat' in services_to_start:
        if args.mode == 'dev':
            cmd = f"python start_api_service.py --host {args.host} --port {args.chat_port} --mode dev"
        else:
            cmd = f"gunicorn --bind {args.host}:{args.chat_port} --workers 4 --timeout 300 api_wsgi:application"
        
        if manager.start_service('chat-service', cmd):
            success_count += 1
            time.sleep(2)  # 等待服务启动
    
    if 'fastapi' in services_to_start:
        if args.mode == 'dev':
            cmd = f"uvicorn fastapi_backend.main:app --host {args.host} --port {args.fastapi_port} --reload"
        else:
            cmd = f"uvicorn fastapi_backend.main:app --host {args.host} --port {args.fastapi_port} --workers 4"
        
        if manager.start_service('fastapi-backend', cmd):
            success_count += 1
            time.sleep(2)  # 等待服务启动
    
    if 'dash' in services_to_start:
        if args.mode == 'dev':
            cmd = f"python app.py"
        else:
            cmd = f"gunicorn --bind {args.host}:{args.dash_port} --workers 4 --timeout 300 wsgi:server"
        
        if manager.start_service('dash-app', cmd):
            success_count += 1
    
    if success_count == 0:
        logger.error("没有成功启动任何服务")
        sys.exit(1)
    
    # 显示服务信息
    logger.info("=" * 50)
    logger.info("🎉 服务启动完成！")
    logger.info("=" * 50)
    
    if 'fastapi' in services_to_start:
        logger.info(f"📱 FastAPI后端: http://{args.host}:{args.fastapi_port}")
        logger.info(f"📚 API文档: http://{args.host}:{args.fastapi_port}/docs")
    
    if 'model' in services_to_start:
        logger.info(f"🤖 模型服务: http://{args.host}:{args.model_port}")
    
    if 'chat' in services_to_start:
        logger.info(f"💬 聊天服务: http://{args.host}:{args.chat_port}")
    
    if 'dash' in services_to_start:
        logger.info(f"🎨 Dash应用: http://{args.host}:{args.dash_port}")
    
    logger.info("=" * 50)
    logger.info("按 Ctrl+C 停止所有服务")
    
    # 等待服务
    manager.wait_for_services()


if __name__ == "__main__":
    main()
