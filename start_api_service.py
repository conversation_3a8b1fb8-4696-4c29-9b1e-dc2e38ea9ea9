#!/usr/bin/env python3
# start_api_service.py - 启动API服务的脚本
import os
import sys
import logging
import argparse
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_dependencies():
    """检查依赖是否安装"""
    required_packages = [
        'flask', 'flask_cors', 'sqlalchemy', 'pymysql', 'requests'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"缺少依赖包: {', '.join(missing_packages)}")
        logger.info("请运行: pip install -r requirements.txt")
        return False
    
    return True


def check_database_connection():
    """检查数据库连接"""
    try:
        from db_models import model_db_engine, chat_db_engine
        
        # 测试模型数据库连接
        with model_db_engine.connect() as conn:
            conn.execute("SELECT 1")
        logger.info("模型数据库连接正常")
        
        # 测试聊天数据库连接
        with chat_db_engine.connect() as conn:
            conn.execute("SELECT 1")
        logger.info("聊天数据库连接正常")
        
        return True
        
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return False


def start_development_server(host='0.0.0.0', port=5002, debug=True):
    """启动开发服务器"""
    logger.info(f"启动开发服务器: http://{host}:{port}")
    
    from api_service import app
    app.run(host=host, port=port, debug=debug)


def start_production_server(host='0.0.0.0', port=5002, workers=4):
    """启动生产服务器"""
    import subprocess
    
    logger.info(f"启动生产服务器: http://{host}:{port} (workers: {workers})")
    
    cmd = [
        'gunicorn',
        '--bind', f'{host}:{port}',
        '--workers', str(workers),
        '--timeout', '300',
        '--access-logfile', '-',
        '--error-logfile', '-',
        'api_wsgi:application'
    ]
    
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        logger.error(f"启动生产服务器失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        logger.info("服务器已停止")


def main():
    parser = argparse.ArgumentParser(description='启动InspirFlow API服务')
    parser.add_argument('--host', default='0.0.0.0', help='绑定主机地址')
    parser.add_argument('--port', type=int, default=5002, help='绑定端口')
    parser.add_argument('--mode', choices=['dev', 'prod'], default='dev', 
                       help='运行模式: dev (开发) 或 prod (生产)')
    parser.add_argument('--workers', type=int, default=4, 
                       help='生产模式下的worker数量')
    parser.add_argument('--skip-checks', action='store_true', 
                       help='跳过依赖和数据库检查')
    
    args = parser.parse_args()
    
    logger.info("InspirFlow API服务启动中...")
    
    # 检查依赖和数据库连接
    if not args.skip_checks:
        if not check_dependencies():
            sys.exit(1)
        
        if not check_database_connection():
            logger.warning("数据库连接检查失败，但继续启动服务")
    
    # 启动服务器
    try:
        if args.mode == 'dev':
            start_development_server(args.host, args.port, debug=True)
        else:
            start_production_server(args.host, args.port, args.workers)
    except KeyboardInterrupt:
        logger.info("服务已停止")
    except Exception as e:
        logger.error(f"启动服务失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
