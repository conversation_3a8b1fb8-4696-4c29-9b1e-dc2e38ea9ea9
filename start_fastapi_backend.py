#!/usr/bin/env python3
# start_fastapi_backend.py - 启动FastAPI后端服务

import os
import sys
import argparse
import logging
import subprocess
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_dependencies():
    """检查必要的依赖"""
    required_packages = [
        'fastapi',
        'uvicorn',
        'sqlalchemy',
        'pymysql',
        'pydantic'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"缺少必要的依赖包: {', '.join(missing_packages)}")
        logger.info("请运行: pip install fastapi uvicorn")
        return False
    
    return True


def check_database_connection():
    """检查数据库连接"""
    try:
        from db_models import chat_db_engine, model_db_engine
        
        # 测试聊天数据库连接
        with chat_db_engine.connect() as conn:
            conn.execute("SELECT 1")
        logger.info("聊天数据库连接正常")
        
        # 测试模型数据库连接
        with model_db_engine.connect() as conn:
            conn.execute("SELECT 1")
        logger.info("模型数据库连接正常")
        
        return True
        
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return False


def create_frontend_directories():
    """创建前端目录结构"""
    frontend_dirs = [
        "frontend",
        "frontend/static",
        "frontend/static/css",
        "frontend/static/js",
        "frontend/static/images"
    ]
    
    for dir_path in frontend_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        logger.info(f"创建目录: {dir_path}")


def start_development_server(host='0.0.0.0', port=8000):
    """启动开发服务器"""
    logger.info(f"启动开发服务器: http://{host}:{port}")
    
    try:
        import uvicorn
        uvicorn.run(
            "fastapi_backend.main:app",
            host=host,
            port=port,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        logger.info("服务器已停止")
    except Exception as e:
        logger.error(f"启动开发服务器失败: {e}")
        sys.exit(1)


def start_production_server(host='0.0.0.0', port=8000, workers=4):
    """启动生产服务器"""
    logger.info(f"启动生产服务器: http://{host}:{port} (workers: {workers})")
    
    cmd = [
        'uvicorn',
        'fastapi_backend.main:app',
        '--host', host,
        '--port', str(port),
        '--workers', str(workers),
        '--access-log',
        '--log-level', 'info'
    ]
    
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        logger.error(f"启动生产服务器失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        logger.info("服务器已停止")


def main():
    parser = argparse.ArgumentParser(description='启动InspirFlow FastAPI后端服务')
    parser.add_argument('--host', default='0.0.0.0', help='绑定主机地址')
    parser.add_argument('--port', type=int, default=8000, help='绑定端口')
    parser.add_argument('--mode', choices=['dev', 'prod'], default='dev', 
                       help='运行模式: dev (开发) 或 prod (生产)')
    parser.add_argument('--workers', type=int, default=4, 
                       help='生产模式下的worker数量')
    parser.add_argument('--skip-checks', action='store_true', 
                       help='跳过依赖和数据库检查')
    
    args = parser.parse_args()
    
    logger.info("InspirFlow FastAPI后端启动中...")
    
    # 创建前端目录
    create_frontend_directories()
    
    # 检查依赖和数据库连接
    if not args.skip_checks:
        if not check_dependencies():
            sys.exit(1)
        
        if not check_database_connection():
            logger.warning("数据库连接检查失败，但继续启动服务")
    
    # 启动服务器
    if args.mode == 'dev':
        start_development_server(args.host, args.port)
    else:
        start_production_server(args.host, args.port, args.workers)


if __name__ == "__main__":
    main()
