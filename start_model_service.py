#!/usr/bin/env python3
# start_model_service.py - 启动模型应用服务（5001端口）

import os
import sys
import argparse
import logging
import subprocess
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_dependencies():
    """检查必要的依赖"""
    required_packages = [
        'flask',
        'flask_cors',
        'sqlalchemy',
        'pymysql'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"缺少必要的依赖包: {', '.join(missing_packages)}")
        logger.info("请运行: pip install -r requirements.txt")
        return False
    
    return True


def check_database_connection():
    """检查数据库连接"""
    try:
        from db_models import model_db_engine
        from sqlalchemy import text

        # 测试模型数据库连接
        with model_db_engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        logger.info("模型数据库连接正常")

        return True

    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return False


def create_model_service_app():
    """创建模型服务Flask应用"""
    from flask import Flask, jsonify, request
    from flask_cors import CORS
    from api_db_operations import APIDBOperations
    
    app = Flask(__name__)
    CORS(app)
    
    @app.route('/health', methods=['GET'])
    def health_check():
        """健康检查"""
        return jsonify({
            'status': 'healthy',
            'service': 'model-service',
            'port': 5001
        })
    
    @app.route('/api/v1/models', methods=['GET'])
    def get_all_models():
        """获取所有模型列表"""
        try:
            from db_operator import get_all_models
            models = get_all_models()
            return jsonify({
                'success': True,
                'models': models
            })
        except Exception as e:
            logger.error(f"获取模型列表失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/v1/models/<int:model_id>', methods=['GET'])
    def get_model_info(model_id):
        """获取指定模型信息"""
        try:
            from db_operator import get_model_data
            model = get_model_data(model_id)
            if not model:
                return jsonify({
                    'success': False,
                    'error': 'Model not found'
                }), 404
            
            return jsonify({
                'success': True,
                'model': model
            })
        except Exception as e:
            logger.error(f"获取模型信息失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/v1/platforms', methods=['GET'])
    def get_platforms():
        """获取所有平台列表"""
        try:
            from api_db_operations import APIDBOperations
            platforms = APIDBOperations.get_all_platforms()
            return jsonify({
                'success': True,
                'platforms': platforms
            })
        except Exception as e:
            logger.error(f"获取平台列表失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/v1/applications', methods=['GET'])
    def get_applications():
        """获取所有应用列表"""
        try:
            applications = APIDBOperations.get_all_applications()
            return jsonify({
                'success': True,
                'applications': applications
            })
        except Exception as e:
            logger.error(f"获取应用列表失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/v1/applications/<int:app_id>/models', methods=['GET'])
    def get_application_models(app_id):
        """获取应用授权的模型列表"""
        try:
            models = APIDBOperations.get_application_models(app_id)
            return jsonify({
                'success': True,
                'models': models
            })
        except Exception as e:
            logger.error(f"获取应用模型列表失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    return app


def start_development_server(host='0.0.0.0', port=5001):
    """启动开发服务器"""
    logger.info(f"启动模型服务开发服务器: http://{host}:{port}")
    
    try:
        app = create_model_service_app()
        app.run(host=host, port=port, debug=True)
    except KeyboardInterrupt:
        logger.info("服务器已停止")
    except Exception as e:
        logger.error(f"启动开发服务器失败: {e}")
        sys.exit(1)


def start_production_server(host='0.0.0.0', port=5001, workers=4):
    """启动生产服务器"""
    logger.info(f"启动模型服务生产服务器: http://{host}:{port} (workers: {workers})")
    
    # 创建WSGI文件
    wsgi_content = '''
from start_model_service import create_model_service_app
application = create_model_service_app()

if __name__ == "__main__":
    application.run(host='0.0.0.0', port=5001)
'''
    
    with open('model_service_wsgi.py', 'w') as f:
        f.write(wsgi_content)
    
    cmd = [
        'gunicorn',
        '--bind', f'{host}:{port}',
        '--workers', str(workers),
        '--timeout', '300',
        '--access-logfile', '-',
        '--error-logfile', '-',
        'model_service_wsgi:application'
    ]
    
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        logger.error(f"启动生产服务器失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        logger.info("服务器已停止")


def main():
    parser = argparse.ArgumentParser(description='启动InspirFlow模型服务')
    parser.add_argument('--host', default='0.0.0.0', help='绑定主机地址')
    parser.add_argument('--port', type=int, default=5001, help='绑定端口')
    parser.add_argument('--mode', choices=['dev', 'prod'], default='dev', 
                       help='运行模式: dev (开发) 或 prod (生产)')
    parser.add_argument('--workers', type=int, default=4, 
                       help='生产模式下的worker数量')
    parser.add_argument('--skip-checks', action='store_true', 
                       help='跳过依赖和数据库检查')
    
    args = parser.parse_args()
    
    logger.info("InspirFlow模型服务启动中...")
    
    # 检查依赖和数据库连接
    if not args.skip_checks:
        if not check_dependencies():
            sys.exit(1)
        
        if not check_database_connection():
            logger.warning("数据库连接检查失败，但继续启动服务")
    
    # 启动服务器
    if args.mode == 'dev':
        start_development_server(args.host, args.port)
    else:
        start_production_server(args.host, args.port, args.workers)


if __name__ == "__main__":
    main()
