#!/usr/bin/env python3
# start_simple.py - 简化的启动脚本

import os
import sys
import logging
import argparse
import subprocess
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_and_install_dependencies():
    """检查并安装依赖"""
    logger.info("检查依赖...")
    
    required_packages = ['fastapi', 'uvicorn', 'gunicorn', 'httpx']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.info(f"安装缺少的依赖: {', '.join(missing_packages)}")
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install"
            ] + missing_packages, check=True)
            logger.info("✅ 依赖安装完成")
        except subprocess.CalledProcessError as e:
            logger.error(f"依赖安装失败: {e}")
            return False
    else:
        logger.info("✅ 所有依赖已安装")
    
    return True


def create_directories():
    """创建必要的目录"""
    directories = [
        "fastapi_backend",
        "fastapi_backend/models", 
        "fastapi_backend/routes",
        "fastapi_backend/services",
        "frontend",
        "frontend/static",
        "frontend/static/css",
        "frontend/static/js"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    logger.info("✅ 目录结构创建完成")


def start_fastapi_only(host='0.0.0.0', port=8000):
    """仅启动FastAPI服务"""
    logger.info(f"启动FastAPI服务: http://{host}:{port}")
    
    try:
        import uvicorn
        uvicorn.run(
            "fastapi_backend.main:app",
            host=host,
            port=port,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        logger.info("服务已停止")
    except Exception as e:
        logger.error(f"启动服务失败: {e}")


def start_with_external_services(host='0.0.0.0', fastapi_port=8000, model_port=5001, chat_port=5002):
    """启动FastAPI和外部服务"""
    import threading
    import time
    
    def start_model_service():
        """启动模型服务"""
        try:
            subprocess.run([
                sys.executable, "start_model_service.py", 
                "--host", host, "--port", str(model_port), "--mode", "dev"
            ])
        except Exception as e:
            logger.error(f"模型服务启动失败: {e}")
    
    def start_chat_service():
        """启动聊天服务"""
        try:
            subprocess.run([
                sys.executable, "start_api_service.py",
                "--host", host, "--port", str(chat_port), "--mode", "dev"
            ])
        except Exception as e:
            logger.error(f"聊天服务启动失败: {e}")
    
    # 启动外部服务
    logger.info("启动外部服务...")
    
    model_thread = threading.Thread(target=start_model_service, daemon=True)
    chat_thread = threading.Thread(target=start_chat_service, daemon=True)
    
    model_thread.start()
    time.sleep(2)  # 等待模型服务启动
    
    chat_thread.start()
    time.sleep(2)  # 等待聊天服务启动
    
    # 启动FastAPI
    logger.info(f"启动FastAPI服务: http://{host}:{fastapi_port}")
    start_fastapi_only(host, fastapi_port)


def main():
    parser = argparse.ArgumentParser(description='InspirFlow 简化启动脚本')
    parser.add_argument('--host', default='0.0.0.0', help='绑定主机地址')
    parser.add_argument('--port', type=int, default=8000, help='FastAPI端口')
    parser.add_argument('--mode', choices=['simple', 'full'], default='simple',
                       help='启动模式: simple(仅FastAPI) 或 full(包含外部服务)')
    parser.add_argument('--skip-deps', action='store_true', help='跳过依赖检查')
    
    args = parser.parse_args()
    
    logger.info("🚀 InspirFlow 简化启动")
    logger.info("=" * 40)
    
    # 创建目录
    create_directories()
    
    # 检查依赖
    if not args.skip_deps:
        if not check_and_install_dependencies():
            sys.exit(1)
    
    # 显示启动信息
    logger.info("=" * 40)
    logger.info("🎉 服务启动中...")
    logger.info("=" * 40)
    logger.info(f"📱 前端界面: http://{args.host}:{args.port}")
    logger.info(f"📚 API文档: http://{args.host}:{args.port}/docs")
    
    if args.mode == 'full':
        logger.info(f"🤖 模型服务: http://{args.host}:5001")
        logger.info(f"💬 聊天服务: http://{args.host}:5002")
    
    logger.info("=" * 40)
    logger.info("按 Ctrl+C 停止服务")
    logger.info("=" * 40)
    
    # 启动服务
    try:
        if args.mode == 'simple':
            start_fastapi_only(args.host, args.port)
        else:
            start_with_external_services(args.host, args.port)
    except KeyboardInterrupt:
        logger.info("服务已停止")


if __name__ == "__main__":
    main()
