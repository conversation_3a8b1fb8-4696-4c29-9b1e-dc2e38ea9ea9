#!/usr/bin/env python3
# test_api_service.py - API服务测试脚本
import requests
import json
import time
import logging
import argparse
from typing import Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class APITester:
    """API服务测试类"""
    
    def __init__(self, base_url: str = "http://localhost:5002/api/v1", api_key: str = None):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.headers = {
            "Content-Type": "application/json"
        }
        if api_key:
            self.headers["Authorization"] = f"Bearer {api_key}"
    
    def test_health(self) -> bool:
        """测试健康检查端点"""
        logger.info("测试健康检查端点...")
        
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"健康检查成功: {data}")
                return True
            else:
                logger.error(f"健康检查失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"健康检查异常: {e}")
            return False
    
    def test_models_endpoint(self) -> bool:
        """测试模型列表端点"""
        logger.info("测试模型列表端点...")
        
        if not self.api_key:
            logger.error("需要API密钥来测试模型端点")
            return False
        
        try:
            response = requests.get(
                f"{self.base_url}/models",
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"获取模型列表成功:")
                logger.info(f"  应用: {data.get('application')}")
                logger.info(f"  模型数量: {len(data.get('models', []))}")
                
                for model in data.get('models', []):
                    default_mark = " (默认)" if model.get('is_default') else ""
                    logger.info(f"    - {model.get('name')} ({model.get('platform')}){default_mark}")
                
                return True
            else:
                logger.error(f"获取模型列表失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"获取模型列表异常: {e}")
            return False
    
    def test_chat_completion(self, model: str = None, stream: bool = False) -> bool:
        """测试聊天完成端点"""
        logger.info(f"测试聊天完成端点 (stream={stream})...")
        
        if not self.api_key:
            logger.error("需要API密钥来测试聊天端点")
            return False
        
        # 准备测试数据
        test_data = {
            "messages": [
                {"role": "user", "content": "Hello! Please respond with a short greeting."}
            ],
            "stream": stream,
            "temperature": 0.7,
            "max_tokens": 100
        }
        
        if model:
            test_data["model"] = model
        
        try:
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=test_data,
                timeout=30,
                stream=stream
            )
            
            if response.status_code == 200:
                if stream:
                    logger.info("流式响应测试:")
                    content_received = False
                    for chunk in response.iter_lines():
                        if chunk:
                            chunk_str = chunk.decode('utf-8')
                            if chunk_str.startswith('data: '):
                                data_str = chunk_str[6:]
                                if data_str.strip() == '[DONE]':
                                    break
                                try:
                                    chunk_data = json.loads(data_str)
                                    if 'choices' in chunk_data and chunk_data['choices']:
                                        delta = chunk_data['choices'][0].get('delta', {})
                                        if 'content' in delta:
                                            print(delta['content'], end='', flush=True)
                                            content_received = True
                                except json.JSONDecodeError:
                                    continue
                    
                    if content_received:
                        print()  # 换行
                        logger.info("流式聊天完成测试成功")
                        return True
                    else:
                        logger.error("流式响应中没有收到内容")
                        return False
                else:
                    data = response.json()
                    if 'choices' in data and data['choices']:
                        content = data['choices'][0].get('message', {}).get('content', '')
                        logger.info(f"聊天完成测试成功:")
                        logger.info(f"  响应内容: {content[:100]}...")
                        return True
                    else:
                        logger.error(f"响应格式异常: {data}")
                        return False
            else:
                logger.error(f"聊天完成失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"聊天完成异常: {e}")
            return False
    
    def test_authentication(self) -> bool:
        """测试认证机制"""
        logger.info("测试认证机制...")
        
        # 测试无认证头
        try:
            response = requests.get(f"{self.base_url}/models", timeout=10)
            if response.status_code == 401:
                logger.info("无认证头测试通过 (401)")
            else:
                logger.warning(f"无认证头应返回401，实际返回: {response.status_code}")
        except Exception as e:
            logger.error(f"无认证头测试异常: {e}")
            return False
        
        # 测试无效API密钥
        try:
            invalid_headers = {
                "Authorization": "Bearer invalid-api-key",
                "Content-Type": "application/json"
            }
            response = requests.get(
                f"{self.base_url}/models",
                headers=invalid_headers,
                timeout=10
            )
            if response.status_code == 401:
                logger.info("无效API密钥测试通过 (401)")
            else:
                logger.warning(f"无效API密钥应返回401，实际返回: {response.status_code}")
        except Exception as e:
            logger.error(f"无效API密钥测试异常: {e}")
            return False
        
        return True
    
    def run_all_tests(self, model: str = None) -> Dict[str, bool]:
        """运行所有测试"""
        logger.info("开始运行API服务测试套件...")
        
        results = {}
        
        # 健康检查测试
        results['health'] = self.test_health()
        
        # 认证测试
        results['authentication'] = self.test_authentication()
        
        if self.api_key:
            # 模型列表测试
            results['models'] = self.test_models_endpoint()
            
            # 聊天完成测试 (非流式)
            results['chat_completion'] = self.test_chat_completion(model, stream=False)
            
            # 聊天完成测试 (流式)
            results['chat_completion_stream'] = self.test_chat_completion(model, stream=True)
        else:
            logger.warning("未提供API密钥，跳过需要认证的测试")
        
        # 输出测试结果
        logger.info("\n" + "="*50)
        logger.info("测试结果汇总:")
        for test_name, result in results.items():
            status = "✓ 通过" if result else "✗ 失败"
            logger.info(f"  {test_name}: {status}")
        
        passed = sum(results.values())
        total = len(results)
        logger.info(f"\n总计: {passed}/{total} 个测试通过")
        
        return results


def main():
    parser = argparse.ArgumentParser(description='InspirFlow API服务测试')
    parser.add_argument('--url', default='http://localhost:5002/api/v1', 
                       help='API服务基础URL')
    parser.add_argument('--api-key', help='API密钥')
    parser.add_argument('--model', help='指定测试的模型名称')
    parser.add_argument('--test', choices=['health', 'auth', 'models', 'chat', 'stream', 'all'],
                       default='all', help='指定要运行的测试')
    
    args = parser.parse_args()
    
    tester = APITester(args.url, args.api_key)
    
    if args.test == 'all':
        results = tester.run_all_tests(args.model)
        # 如果有测试失败，退出码为1
        if not all(results.values()):
            exit(1)
    elif args.test == 'health':
        success = tester.test_health()
        exit(0 if success else 1)
    elif args.test == 'auth':
        success = tester.test_authentication()
        exit(0 if success else 1)
    elif args.test == 'models':
        success = tester.test_models_endpoint()
        exit(0 if success else 1)
    elif args.test == 'chat':
        success = tester.test_chat_completion(args.model, stream=False)
        exit(0 if success else 1)
    elif args.test == 'stream':
        success = tester.test_chat_completion(args.model, stream=True)
        exit(0 if success else 1)


if __name__ == '__main__':
    main()
