#!/usr/bin/env python3
# test_fastapi_backend.py - FastAPI后端测试脚本

import asyncio
import json
import logging
from typing import Dict, Any
import httpx

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FastAPIBackendTester:
    """FastAPI后端测试类"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.api_base = f"{base_url}/api/v1"
        self.access_token = None
        self.test_api_key = None
        
    async def test_health_check(self):
        """测试健康检查端点"""
        logger.info("测试健康检查...")
        
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{self.base_url}/health")
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ 健康检查通过: {data}")
                return True
            else:
                logger.error(f"❌ 健康检查失败: {response.status_code}")
                return False
    
    async def test_login(self, api_key: str):
        """测试登录功能"""
        logger.info("测试用户登录...")
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.api_base}/auth/login",
                json={"api_key": api_key}
            )
            
            if response.status_code == 200:
                data = response.json()
                self.access_token = data["access_token"]
                logger.info(f"✅ 登录成功: 用户ID {data['user_id']}")
                return True
            else:
                logger.error(f"❌ 登录失败: {response.status_code} - {response.text}")
                return False
    
    def get_headers(self) -> Dict[str, str]:
        """获取认证头"""
        if not self.access_token:
            return {}
        return {"Authorization": f"Bearer {self.access_token}"}
    
    async def test_user_info(self):
        """测试获取用户信息"""
        logger.info("测试获取用户信息...")
        
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.api_base}/users/me",
                headers=self.get_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ 获取用户信息成功: ID {data['id']}")
                return True
            else:
                logger.error(f"❌ 获取用户信息失败: {response.status_code}")
                return False
    
    async def test_conversations(self):
        """测试对话管理"""
        logger.info("测试对话管理...")
        
        async with httpx.AsyncClient() as client:
            # 获取对话列表
            response = await client.get(
                f"{self.api_base}/conversations/",
                headers=self.get_headers()
            )
            
            if response.status_code != 200:
                logger.error(f"❌ 获取对话列表失败: {response.status_code}")
                return False
            
            conversations = response.json()["conversations"]
            logger.info(f"✅ 获取对话列表成功: {len(conversations)} 个对话")
            
            # 创建新对话
            response = await client.post(
                f"{self.api_base}/conversations/",
                headers=self.get_headers()
            )
            
            if response.status_code == 200:
                new_conversation = response.json()["conversation"]
                logger.info(f"✅ 创建对话成功: ID {new_conversation['id']}")
                return new_conversation["id"]
            else:
                logger.error(f"❌ 创建对话失败: {response.status_code}")
                return False
    
    async def test_messages(self, conversation_id: int):
        """测试消息管理"""
        logger.info("测试消息管理...")
        
        async with httpx.AsyncClient() as client:
            # 发送消息
            response = await client.post(
                f"{self.api_base}/messages/send",
                json={
                    "conversation_id": conversation_id,
                    "content": "这是一条测试消息"
                },
                headers=self.get_headers()
            )
            
            if response.status_code == 200:
                message = response.json()
                logger.info(f"✅ 发送消息成功: ID {message['id']}")
                
                # 获取对话消息
                response = await client.get(
                    f"{self.api_base}/messages/conversation/{conversation_id}",
                    headers=self.get_headers()
                )
                
                if response.status_code == 200:
                    messages = response.json()["messages"]
                    logger.info(f"✅ 获取消息列表成功: {len(messages)} 条消息")
                    return True
                else:
                    logger.error(f"❌ 获取消息列表失败: {response.status_code}")
                    return False
            else:
                logger.error(f"❌ 发送消息失败: {response.status_code}")
                return False
    
    async def test_models(self):
        """测试模型管理"""
        logger.info("测试模型管理...")
        
        async with httpx.AsyncClient() as client:
            # 获取模型列表
            response = await client.get(
                f"{self.api_base}/models/",
                headers=self.get_headers()
            )
            
            if response.status_code == 200:
                models = response.json()["models"]
                logger.info(f"✅ 获取模型列表成功: {len(models)} 个模型")
                
                # 获取用户当前模型设置
                response = await client.get(
                    f"{self.api_base}/models/user/current",
                    headers=self.get_headers()
                )
                
                if response.status_code == 200:
                    settings = response.json()
                    logger.info(f"✅ 获取用户模型设置成功: 模型ID {settings['current_model_id']}")
                    return True
                else:
                    logger.error(f"❌ 获取用户模型设置失败: {response.status_code}")
                    return False
            else:
                logger.error(f"❌ 获取模型列表失败: {response.status_code}")
                return False
    
    async def test_chat_api(self):
        """测试聊天API（OpenAI兼容）"""
        logger.info("测试聊天API...")
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.api_base}/chat/completions",
                json={
                    "messages": [
                        {"role": "user", "content": "你好"}
                    ],
                    "stream": False
                },
                headers=self.get_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ 聊天API测试成功: {data['choices'][0]['message']['content'][:50]}...")
                return True
            else:
                logger.error(f"❌ 聊天API测试失败: {response.status_code}")
                return False
    
    async def run_all_tests(self, api_key: str):
        """运行所有测试"""
        logger.info("开始运行FastAPI后端测试...")
        
        test_results = []
        
        # 健康检查
        result = await self.test_health_check()
        test_results.append(("健康检查", result))
        
        # 用户登录
        result = await self.test_login(api_key)
        test_results.append(("用户登录", result))
        
        if not result:
            logger.error("登录失败，跳过后续测试")
            return test_results
        
        # 用户信息
        result = await self.test_user_info()
        test_results.append(("用户信息", result))
        
        # 对话管理
        conversation_id = await self.test_conversations()
        test_results.append(("对话管理", bool(conversation_id)))
        
        # 消息管理
        if conversation_id:
            result = await self.test_messages(conversation_id)
            test_results.append(("消息管理", result))
        
        # 模型管理
        result = await self.test_models()
        test_results.append(("模型管理", result))
        
        # 聊天API
        result = await self.test_chat_api()
        test_results.append(("聊天API", result))
        
        # 输出测试结果
        logger.info("\n" + "="*50)
        logger.info("测试结果汇总:")
        logger.info("="*50)
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"{test_name:<15} {status}")
            if result:
                passed += 1
        
        logger.info("="*50)
        logger.info(f"总计: {passed}/{total} 个测试通过")
        
        return test_results


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='测试FastAPI后端')
    parser.add_argument('--url', default='http://localhost:8000', help='后端URL')
    parser.add_argument('--api-key', required=True, help='测试用的API密钥')
    
    args = parser.parse_args()
    
    tester = FastAPIBackendTester(args.url)
    await tester.run_all_tests(args.api_key)


if __name__ == "__main__":
    asyncio.run(main())
