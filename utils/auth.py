# auth.py
from datetime import datetime, timedelta
import secrets
import jwt
import logging

# 初始化日志
logger = logging.getLogger(__name__)

# 安全密钥
SECRET_KEY = secrets.token_hex(32)  # 生成随机密钥

def create_access_token(api_key, expires_delta=timedelta(hours=24)):
    """创建JWT访问令牌"""
    expire = datetime.utcnow() + expires_delta
    to_encode = {"exp": expire, "api_key": api_key}
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm="HS256")
    return encoded_jwt

def verify_token(token):
    """验证JWT令牌"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
        return payload["api_key"]
    except:
        return None

def verify_access_token(token):
    """验证访问令牌（别名函数，保持兼容性）"""
    return verify_token(token)